import React, { useState, useEffect, useCallback } from 'react';
import { useWebSocket } from './hooks/useWebSocket';
import { ApiService } from './services/api';
import { Candlestick, TopLevel, ChartData } from './types';
import Enhanced<PERSON>hart from './components/EnhancedChart';

// Simple chart component without lightweight-charts (which was causing the crash)
const SimpleChart: React.FC<{
  candlesticks: Candlestick[];
  topLevels: TopLevel[];
}> = ({ candlesticks, topLevels }) => {
  const latestPrice = candlesticks.length > 0 ? candlesticks[candlesticks.length - 1].close : 0;
  const priceChange = candlesticks.length > 1 ?
    candlesticks[candlesticks.length - 1].close - candlesticks[candlesticks.length - 2].close : 0;

  return (
    <div style={{
      background: '#1a1a1a',
      borderRadius: '6px',
      padding: '16px',
      height: '100%',
      display: 'flex',
      flexDirection: 'column'
    }}>
      <div style={{ marginBottom: '16px', flexShrink: 0 }}>
        <h2 style={{
          margin: '0 0 8px 0',
          color: 'white',
          fontSize: '16px',
          fontWeight: '600'
        }}>BTCUSDT Perpetual</h2>
        <div style={{ display: 'flex', gap: '12px', alignItems: 'center' }}>
          <span style={{
            fontSize: '24px',
            fontWeight: '700',
            color: '#22c55e'
          }}>
            ${latestPrice.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
          </span>
          <span style={{
            color: priceChange >= 0 ? '#22c55e' : '#ef4444',
            fontSize: '12px'
          }}>
            {priceChange >= 0 ? '+' : ''}{priceChange.toFixed(2)}
          </span>
          <span style={{ color: '#666', fontSize: '12px' }}>
            {topLevels.filter(l => l.is_active).length} Active Levels
          </span>
        </div>
      </div>

      {/* Chart visualization */}
      <div style={{
        background: '#0f0f0f',
        flex: 1,
        borderRadius: '4px',
        position: 'relative',
        overflow: 'hidden',
        minHeight: '300px'
      }}>
        {/* Price bars visualization */}
        <div style={{
          display: 'flex',
          alignItems: 'end',
          height: '100%',
          padding: '8px',
          gap: '1px'
        }}>
          {candlesticks.slice(-80).map((candle, i) => {
            const maxPrice = Math.max(...candlesticks.slice(-80).map(c => Math.max(c.high, c.open, c.close, c.low)));
            const minPrice = Math.min(...candlesticks.slice(-80).map(c => Math.min(c.high, c.open, c.close, c.low)));
            const priceRange = maxPrice - minPrice;
            const normalizedHeight = priceRange > 0 ? ((candle.close - minPrice) / priceRange) * 280 : 50;
            const height = Math.max(2, normalizedHeight);
            const isGreen = candle.close > candle.open;
            return (
              <div
                key={i}
                style={{
                  width: '3px',
                  height: `${height}px`,
                  backgroundColor: isGreen ? '#22c55e' : '#ef4444',
                  opacity: 0.9,
                  flex: 1
                }}
              />
            );
          })}
        </div>

        {/* Top levels as horizontal lines */}
        {topLevels.filter(l => l.is_active).slice(0, 6).map((level, i) => {
          if (!latestPrice || latestPrice === 0 || !level.price) return null;

          const candlestickData = candlesticks.slice(-80);
          const maxPrice = Math.max(...candlestickData.map(c => Math.max(c.high, c.open, c.close, c.low)));
          const minPrice = Math.min(...candlestickData.map(c => Math.min(c.high, c.open, c.close, c.low)));
          const priceRange = maxPrice - minPrice;

          if (priceRange === 0) return null;

          const normalizedPosition = (level.price - minPrice) / priceRange;
          const yPosition = Math.max(8, Math.min(280, (1 - normalizedPosition) * 280 + 8));

          return (
            <div key={level.id || i}>
              {/* Horizontal line spanning chart width */}
              <div
                style={{
                  position: 'absolute',
                  left: '8px',
                  right: '8px',
                  top: `${yPosition}px`,
                  height: '1px',
                  background: level.level_type === 'support' ? '#22c55e' : '#ef4444',
                  opacity: 0.8,
                  zIndex: 2
                }}
              />
              {/* Price label on the right */}
              <div
                style={{
                  position: 'absolute',
                  right: '12px',
                  top: `${yPosition - 8}px`,
                  background: level.level_type === 'support' ? '#22c55e' : '#ef4444',
                  color: 'white',
                  padding: '1px 4px',
                  borderRadius: '2px',
                  fontSize: '10px',
                  fontWeight: '500',
                  zIndex: 3
                }}
              >
                ${level.price.toFixed(2)}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

// Simple sidebar component
const SimpleSidebar: React.FC<{
  topLevels: TopLevel[];
  isConnected: boolean;
  currentPrice: number;
}> = ({ topLevels, isConnected, currentPrice }) => {
  // Backend now handles 5% filtering, so just filter active levels
  const supportLevels = topLevels.filter(level =>
    level.level_type === 'support' && level.is_active
  ).sort((a, b) => b.price - a.price);

  const resistanceLevels = topLevels.filter(level =>
    level.level_type === 'resistance' && level.is_active
  ).sort((a, b) => a.price - b.price);

  return (
    <div style={{
      background: '#1a1a1a',
      borderRadius: '6px',
      padding: '16px',
      color: 'white',
      height: '100%',
      display: 'flex',
      flexDirection: 'column'
    }}>
      <div style={{ marginBottom: '16px', flexShrink: 0 }}>
        <h3 style={{
          margin: '0 0 8px 0',
          fontSize: '16px',
          fontWeight: '600'
        }}>Top Levels</h3>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '6px',
          fontSize: '12px'
        }}>
          <div style={{
            width: '6px',
            height: '6px',
            borderRadius: '50%',
            background: isConnected ? '#22c55e' : '#ef4444'
          }}></div>
          {isConnected ? 'Live' : 'Offline'}
        </div>
      </div>

      <div style={{ marginBottom: '24px', flex: 1, overflow: 'auto' }}>
        <h4 style={{
          color: '#ef4444',
          fontSize: '12px',
          textTransform: 'uppercase',
          margin: '0 0 12px 0',
          letterSpacing: '0.5px',
          fontWeight: '600'
        }}>
          RESISTANCE ({resistanceLevels.length})
        </h4>
        <div style={{ maxHeight: '200px', overflow: 'auto' }}>
          {resistanceLevels.slice(0, 5).map((level, index) => (
            <div key={level.id || index} style={{
              padding: '8px 0',
              borderBottom: index < resistanceLevels.slice(0, 5).length - 1 ? '1px solid #2a2a2a' : 'none'
            }}>
              <div style={{
                fontWeight: '700',
                color: '#ef4444',
                fontSize: '14px'
              }}>
                ${level.price.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
              </div>
              <div style={{
                fontSize: '11px',
                color: '#666',
                marginTop: '2px'
              }}>
                {level.touch_count} touches • Vol {level.volume_sum.toFixed(0)}
              </div>
            </div>
          ))}
        </div>
      </div>

      <div style={{ flex: 1, overflow: 'auto' }}>
        <h4 style={{
          color: '#22c55e',
          fontSize: '12px',
          textTransform: 'uppercase',
          margin: '0 0 12px 0',
          letterSpacing: '0.5px',
          fontWeight: '600'
        }}>
          SUPPORT ({supportLevels.length})
        </h4>
        <div style={{ maxHeight: '200px', overflow: 'auto' }}>
          {supportLevels.slice(0, 5).map((level, index) => (
            <div key={level.id || index} style={{
              padding: '8px 0',
              borderBottom: index < supportLevels.slice(0, 5).length - 1 ? '1px solid #2a2a2a' : 'none'
            }}>
              <div style={{
                fontWeight: '700',
                color: '#22c55e',
                fontSize: '14px'
              }}>
                ${level.price.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
              </div>
              <div style={{
                fontSize: '11px',
                color: '#666',
                marginTop: '2px'
              }}>
                {level.touch_count} touches • Vol {level.volume_sum.toFixed(0)}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

const WorkingApp: React.FC = () => {
  const [candlesticks, setCandlesticks] = useState<Candlestick[]>([]);
  const [topLevels, setTopLevels] = useState<TopLevel[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const { isConnected, subscribe, unsubscribe } = useWebSocket();

  // Load initial data
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        console.log('Loading initial data...');
        setLoading(true);
        // Change to 30-minute candlesticks for better level visualization
        const chartData: ChartData = await ApiService.getChartData('30m', undefined, undefined, 100);
        console.log('Chart data loaded:', chartData);
        setCandlesticks(chartData.candlesticks);
        setTopLevels(chartData.topLevels);
        setError(null);
      } catch (err) {
        console.error('Error loading initial data:', err);
        setError(`Failed to load chart data: ${err}`);
      } finally {
        setLoading(false);
      }
    };

    loadInitialData();
  }, []);

  // Handle real-time updates
  const handleCandlestickUpdate = useCallback((newCandlestick: Candlestick) => {
    setCandlesticks(prev => {
      const updated = [...prev];
      const lastIndex = updated.length - 1;
      
      if (lastIndex >= 0 && updated[lastIndex].timestamp === newCandlestick.timestamp) {
        updated[lastIndex] = newCandlestick;
      } else {
        updated.push(newCandlestick);
        if (updated.length > 200) {
          updated.splice(0, updated.length - 200);
        }
      }
      return updated;
    });
  }, []);

  const handleTopLevelsUpdate = useCallback((newTopLevels: TopLevel[]) => {
    console.log('Received top levels update:', newTopLevels);
    setTopLevels(newTopLevels);
  }, []);

  // Subscribe to WebSocket events
  useEffect(() => {
    if (isConnected) {
      subscribe('candlestick', handleCandlestickUpdate);
      subscribe('topLevels', handleTopLevelsUpdate);

      return () => {
        unsubscribe('candlestick', handleCandlestickUpdate);
        unsubscribe('topLevels', handleTopLevelsUpdate);
      };
    }
  }, [isConnected, subscribe, unsubscribe, handleCandlestickUpdate, handleTopLevelsUpdate]);

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh', 
        backgroundColor: '#0a0a0a',
        color: 'white',
        fontFamily: 'Arial, sans-serif'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{
            width: '50px',
            height: '50px',
            border: '3px solid #333',
            borderTop: '3px solid #22c55e',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 20px'
          }}></div>
          <p>Loading TRDR data...</p>
          <style>{`
            @keyframes spin {
              0% { transform: rotate(0deg); }
              100% { transform: rotate(360deg); }
            }
          `}</style>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh', 
        backgroundColor: '#0a0a0a',
        color: 'white',
        fontFamily: 'Arial, sans-serif'
      }}>
        <div style={{ 
          textAlign: 'center',
          background: '#2a0000',
          padding: '30px',
          borderRadius: '8px',
          border: '1px solid #ff4444',
          maxWidth: '500px'
        }}>
          <h2 style={{ color: '#ff4444', margin: '0 0 15px 0' }}>Connection Error</h2>
          <p style={{ margin: '0 0 20px 0' }}>{error}</p>
          <button 
            onClick={() => window.location.reload()}
            style={{
              background: '#22c55e',
              color: 'black',
              border: 'none',
              padding: '12px 24px',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: 'bold'
            }}
          >
            Retry Connection
          </button>
        </div>
      </div>
    );
  }

  // Calculate latest price for the sidebar
  const latestPrice = candlesticks.length > 0 ? candlesticks[candlesticks.length - 1].close : 0;

  return (
    <div style={{
      backgroundColor: '#0a0a0a',
      color: '#ffffff',
      minHeight: '100vh',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      overflow: 'hidden'
    }}>
      {/* Header */}
      <header style={{
        background: '#0a0a0a',
        padding: '12px 20px',
        borderBottom: '1px solid #1a1a1a',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        height: '60px',
        boxSizing: 'border-box'
      }}>
        <div>
          <h1 style={{
            margin: 0,
            fontSize: '18px',
            fontWeight: '600',
            color: '#ffffff'
          }}>TRDR Clone</h1>
          <p style={{
            color: '#666',
            margin: 0,
            fontSize: '12px',
            marginTop: '2px'
          }}>Real-time Trading Levels</p>
        </div>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          fontSize: '12px',
          color: isConnected ? '#22c55e' : '#ef4444'
        }}>
          <div style={{
            width: '6px',
            height: '6px',
            borderRadius: '50%',
            background: 'currentColor'
          }}></div>
          {isConnected ? 'Live' : 'Offline'}
        </div>
      </header>

      {/* Main content */}
      <main style={{
        display: 'flex',
        height: 'calc(100vh - 60px)',
        overflow: 'hidden'
      }}>
        <div style={{
          flex: 1,
          padding: '16px',
          paddingRight: '8px'
        }}>
          <EnhancedChart candlesticks={candlesticks} topLevels={topLevels} />
        </div>
        <div style={{
          width: '280px',
          padding: '16px',
          paddingLeft: '8px'
        }}>
          <SimpleSidebar topLevels={topLevels} isConnected={isConnected} currentPrice={latestPrice} />
        </div>
      </main>
    </div>
  );
};

export default WorkingApp;