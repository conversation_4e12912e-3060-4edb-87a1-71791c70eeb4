import React, { useState, useEffect, useCallback } from 'react';
import { useWebSocket } from './hooks/useWebSocket';
import { ApiService } from './services/api';
import { Candlestick, TopLevel, ChartData } from './types';

// Simple chart component without lightweight-charts (which was causing the crash)
const SimpleChart: React.FC<{ 
  candlesticks: Candlestick[]; 
  topLevels: TopLevel[]; 
}> = ({ candlesticks, topLevels }) => {
  const latestPrice = candlesticks.length > 0 ? candlesticks[candlesticks.length - 1].close : 0;
  const priceChange = candlesticks.length > 1 ? 
    candlesticks[candlesticks.length - 1].close - candlesticks[candlesticks.length - 2].close : 0;

  return (
    <div style={{ 
      background: '#161616', 
      borderRadius: '8px', 
      padding: '20px',
      border: '1px solid #333',
      height: '500px'
    }}>
      <div style={{ marginBottom: '20px' }}>
        <h2 style={{ margin: '0 0 10px 0', color: 'white' }}>BTCUSDT Perpetual</h2>
        <div style={{ display: 'flex', gap: '20px', alignItems: 'center' }}>
          <span style={{ 
            fontSize: '28px', 
            fontWeight: 'bold',
            color: priceChange >= 0 ? '#22c55e' : '#ef4444'
          }}>
            ${latestPrice.toFixed(2)}
          </span>
          <span style={{ 
            color: priceChange >= 0 ? '#22c55e' : '#ef4444',
            fontSize: '16px'
          }}>
            {priceChange >= 0 ? '+' : ''}{priceChange.toFixed(2)}
          </span>
          <span style={{ color: '#888' }}>
            {topLevels.filter(l => l.is_active).length} Active Levels
          </span>
        </div>
      </div>
      
      {/* Simple price visualization */}
      <div style={{ 
        background: '#0a0a0a',
        height: '300px',
        border: '1px solid #333',
        borderRadius: '4px',
        position: 'relative',
        overflow: 'hidden'
      }}>
        {/* Price bars visualization */}
        <div style={{ 
          display: 'flex', 
          alignItems: 'end', 
          height: '100%', 
          padding: '10px',
          gap: '1px'
        }}>
          {candlesticks.slice(-50).map((candle, i) => {
            const height = Math.max(2, (candle.close / latestPrice) * 250);
            const isGreen = candle.close > candle.open;
            return (
              <div
                key={i}
                style={{
                  width: '4px',
                  height: `${height}px`,
                  backgroundColor: isGreen ? '#22c55e' : '#ef4444',
                  opacity: 0.8
                }}
              />
            );
          })}
        </div>
        
        {/* Top levels as horizontal lines */}
        {topLevels.filter(l => l.is_active).slice(0, 8).map((level, i) => {
          if (!latestPrice || latestPrice === 0 || !level.price) return null;
          
          const priceRatio = level.price / latestPrice;
          const yPosition = Math.max(10, Math.min(280, 150 + (1 - priceRatio) * 200));
          
          return (
            <div key={level.id || i}>
              {/* Horizontal line spanning chart width */}
              <div
                style={{
                  position: 'absolute',
                  left: '10px',
                  right: '10px',
                  top: `${yPosition}px`,
                  height: '2px',
                  background: level.level_type === 'support' ? '#22c55e' : '#ef4444',
                  opacity: 0.8,
                  zIndex: 2
                }}
              />
              {/* Price label on the right */}
              <div
                style={{
                  position: 'absolute',
                  right: '15px',
                  top: `${yPosition - 10}px`,
                  background: level.level_type === 'support' ? '#22c55e' : '#ef4444',
                  color: 'white',
                  padding: '2px 6px',
                  borderRadius: '3px',
                  fontSize: '11px',
                  fontWeight: 'bold',
                  zIndex: 3
                }}
              >
                ${level.price.toFixed(2)}
              </div>
            </div>
          );
        })}
        
        <div style={{
          position: 'absolute',
          bottom: '10px',
          left: '10px',
          color: '#888',
          fontSize: '12px'
        }}>
          Last {candlesticks.slice(-50).length} candles • {new Date().toLocaleTimeString()}
        </div>
      </div>
    </div>
  );
};

// Simple sidebar component
const SimpleSidebar: React.FC<{ 
  topLevels: TopLevel[]; 
  isConnected: boolean;
  currentPrice: number;
}> = ({ topLevels, isConnected, currentPrice }) => {
  // Backend now handles 5% filtering, so just filter active levels
  const supportLevels = topLevels.filter(level => 
    level.level_type === 'support' && level.is_active
  ).sort((a, b) => b.price - a.price);

  const resistanceLevels = topLevels.filter(level => 
    level.level_type === 'resistance' && level.is_active
  ).sort((a, b) => a.price - b.price);

  return (
    <div style={{ 
      width: '320px', 
      background: '#161616', 
      border: '1px solid #333',
      borderRadius: '8px',
      padding: '20px',
      color: 'white'
    }}>
      <div style={{ marginBottom: '20px' }}>
        <h3 style={{ margin: '0 0 10px 0' }}>Top Levels</h3>
        <div style={{ 
          display: 'flex', 
          alignItems: 'center', 
          gap: '8px',
          fontSize: '12px'
        }}>
          <div style={{ 
            width: '8px', 
            height: '8px', 
            borderRadius: '50%',
            background: isConnected ? '#22c55e' : '#ef4444'
          }}></div>
          {isConnected ? 'Live' : 'Offline'}
        </div>
      </div>

      <div style={{ marginBottom: '30px' }}>
        <h4 style={{ 
          color: '#ef4444', 
          fontSize: '14px', 
          textTransform: 'uppercase',
          margin: '0 0 15px 0',
          letterSpacing: '0.5px'
        }}>
          Resistance ({resistanceLevels.length})
        </h4>
        {resistanceLevels.slice(0, 5).map((level, index) => (
          <div key={level.id || index} style={{ 
            padding: '10px 0', 
            borderBottom: '1px solid #333' 
          }}>
            <div style={{ 
              fontWeight: 'bold', 
              color: '#ef4444',
              fontSize: '16px'
            }}>
              ${level.price.toFixed(2)}
            </div>
            <div style={{ 
              fontSize: '12px', 
              color: '#888',
              marginTop: '4px'
            }}>
              {level.touch_count} touches • Vol: {level.volume_sum.toFixed(0)}
            </div>
          </div>
        ))}
      </div>

      <div>
        <h4 style={{ 
          color: '#22c55e', 
          fontSize: '14px', 
          textTransform: 'uppercase',
          margin: '0 0 15px 0',
          letterSpacing: '0.5px'
        }}>
          Support ({supportLevels.length})
        </h4>
        {supportLevels.slice(0, 5).map((level, index) => (
          <div key={level.id || index} style={{ 
            padding: '10px 0', 
            borderBottom: '1px solid #333' 
          }}>
            <div style={{ 
              fontWeight: 'bold', 
              color: '#22c55e',
              fontSize: '16px'
            }}>
              ${level.price.toFixed(2)}
            </div>
            <div style={{ 
              fontSize: '12px', 
              color: '#888',
              marginTop: '4px'
            }}>
              {level.touch_count} touches • Vol: {level.volume_sum.toFixed(0)}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

const WorkingApp: React.FC = () => {
  const [candlesticks, setCandlesticks] = useState<Candlestick[]>([]);
  const [topLevels, setTopLevels] = useState<TopLevel[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const { isConnected, subscribe, unsubscribe } = useWebSocket();

  // Load initial data
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        console.log('Loading initial data...');
        setLoading(true);
        const chartData: ChartData = await ApiService.getChartData('1m', undefined, undefined, 200);
        console.log('Chart data loaded:', chartData);
        setCandlesticks(chartData.candlesticks);
        setTopLevels(chartData.topLevels);
        setError(null);
      } catch (err) {
        console.error('Error loading initial data:', err);
        setError(`Failed to load chart data: ${err}`);
      } finally {
        setLoading(false);
      }
    };

    loadInitialData();
  }, []);

  // Handle real-time updates
  const handleCandlestickUpdate = useCallback((newCandlestick: Candlestick) => {
    setCandlesticks(prev => {
      const updated = [...prev];
      const lastIndex = updated.length - 1;
      
      if (lastIndex >= 0 && updated[lastIndex].timestamp === newCandlestick.timestamp) {
        updated[lastIndex] = newCandlestick;
      } else {
        updated.push(newCandlestick);
        if (updated.length > 200) {
          updated.splice(0, updated.length - 200);
        }
      }
      return updated;
    });
  }, []);

  const handleTopLevelsUpdate = useCallback((newTopLevels: TopLevel[]) => {
    console.log('Received top levels update:', newTopLevels);
    setTopLevels(newTopLevels);
  }, []);

  // Subscribe to WebSocket events
  useEffect(() => {
    if (isConnected) {
      subscribe('candlestick', handleCandlestickUpdate);
      subscribe('topLevels', handleTopLevelsUpdate);

      return () => {
        unsubscribe('candlestick', handleCandlestickUpdate);
        unsubscribe('topLevels', handleTopLevelsUpdate);
      };
    }
  }, [isConnected, subscribe, unsubscribe, handleCandlestickUpdate, handleTopLevelsUpdate]);

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh', 
        backgroundColor: '#0a0a0a',
        color: 'white',
        fontFamily: 'Arial, sans-serif'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{
            width: '50px',
            height: '50px',
            border: '3px solid #333',
            borderTop: '3px solid #22c55e',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 20px'
          }}></div>
          <p>Loading TRDR data...</p>
          <style>{`
            @keyframes spin {
              0% { transform: rotate(0deg); }
              100% { transform: rotate(360deg); }
            }
          `}</style>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh', 
        backgroundColor: '#0a0a0a',
        color: 'white',
        fontFamily: 'Arial, sans-serif'
      }}>
        <div style={{ 
          textAlign: 'center',
          background: '#2a0000',
          padding: '30px',
          borderRadius: '8px',
          border: '1px solid #ff4444',
          maxWidth: '500px'
        }}>
          <h2 style={{ color: '#ff4444', margin: '0 0 15px 0' }}>Connection Error</h2>
          <p style={{ margin: '0 0 20px 0' }}>{error}</p>
          <button 
            onClick={() => window.location.reload()}
            style={{
              background: '#22c55e',
              color: 'black',
              border: 'none',
              padding: '12px 24px',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: 'bold'
            }}
          >
            Retry Connection
          </button>
        </div>
      </div>
    );
  }

  // Calculate latest price for the sidebar
  const latestPrice = candlesticks.length > 0 ? candlesticks[candlesticks.length - 1].close : 0;

  return (
    <div style={{
      backgroundColor: '#0a0a0a',
      color: 'white',
      minHeight: '100vh',
      fontFamily: 'Arial, sans-serif'
    }}>
      {/* Header */}
      <header style={{
        background: '#161616',
        padding: '20px 30px',
        borderBottom: '1px solid #333',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <div>
          <h1 style={{ margin: '0 0 5px 0', fontSize: '24px' }}>TRDR Clone</h1>
          <p style={{ color: '#888', margin: 0, fontSize: '14px' }}>Real-time Trading Levels</p>
        </div>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '10px',
          background: isConnected ? 'rgba(34, 197, 94, 0.1)' : 'rgba(239, 68, 68, 0.1)',
          color: isConnected ? '#22c55e' : '#ef4444',
          padding: '8px 16px',
          borderRadius: '6px',
          fontSize: '14px'
        }}>
          <div style={{
            width: '8px',
            height: '8px',
            borderRadius: '50%',
            background: 'currentColor'
          }}></div>
          {isConnected ? 'Live' : 'Offline'}
        </div>
      </header>

      {/* Main content */}
      <main style={{
        display: 'grid',
        gridTemplateColumns: '1fr 320px',
        gap: '20px',
        padding: '20px',
        height: 'calc(100vh - 100px)'
      }}>
        <SimpleChart candlesticks={candlesticks} topLevels={topLevels} />
        <SimpleSidebar topLevels={topLevels} isConnected={isConnected} currentPrice={latestPrice} />
      </main>
    </div>
  );
};

export default WorkingApp;