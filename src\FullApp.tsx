import React, { useState, useEffect, useCallback } from 'react';
import { Chart } from './components/Chart';
import { Sidebar } from './components/Sidebar';
import { useWebSocket } from './hooks/useWebSocket';
import { ApiService } from './services/api';
import { Candlestick, TopLevel, ChartData } from './types';
import './App.css';

const FullApp: React.FC = () => {
  const [candlesticks, setCandlesticks] = useState<Candlestick[]>([]);
  const [topLevels, setTopLevels] = useState<TopLevel[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const { isConnected, subscribe, unsubscribe } = useWebSocket();

  // Load initial data
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        console.log('Loading initial data...');
        setLoading(true);
        const chartData: ChartData = await ApiService.getChartData('1m', undefined, undefined, 500);
        console.log('Chart data loaded:', chartData);
        setCandlesticks(chartData.candlesticks);
        setTopLevels(chartData.topLevels);
        setError(null);
      } catch (err) {
        console.error('Error loading initial data:', err);
        setError(`Failed to load chart data: ${err}`);
      } finally {
        setLoading(false);
      }
    };

    loadInitialData();
  }, []);

  // Handle real-time candlestick updates
  const handleCandlestickUpdate = useCallback((newCandlestick: Candlestick) => {
    setCandlesticks(prev => {
      const updated = [...prev];
      const lastIndex = updated.length - 1;
      
      if (lastIndex >= 0 && updated[lastIndex].timestamp === newCandlestick.timestamp) {
        // Update existing candlestick
        updated[lastIndex] = newCandlestick;
      } else {
        // Add new candlestick
        updated.push(newCandlestick);
        
        // Keep only last 500 candlesticks for performance
        if (updated.length > 500) {
          updated.splice(0, updated.length - 500);
        }
      }
      
      return updated;
    });
  }, []);

  // Handle top levels updates
  const handleTopLevelsUpdate = useCallback((newTopLevels: TopLevel[]) => {
    console.log('Received top levels update:', newTopLevels);
    setTopLevels(newTopLevels);
  }, []);

  // Subscribe to WebSocket events
  useEffect(() => {
    if (isConnected) {
      subscribe('candlestick', handleCandlestickUpdate);
      subscribe('topLevels', handleTopLevelsUpdate);

      return () => {
        unsubscribe('candlestick', handleCandlestickUpdate);
        unsubscribe('topLevels', handleTopLevelsUpdate);
      };
    }
  }, [isConnected, subscribe, unsubscribe, handleCandlestickUpdate, handleTopLevelsUpdate]);

  if (loading) {
    return (
      <div className="app loading">
        <div className="loading-spinner">
          <div className="spinner"></div>
          <p>Loading TRDR data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="app error">
        <div className="error-message">
          <h2>Error</h2>
          <p>{error}</p>
          <button onClick={() => window.location.reload()}>
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="app">
      <header className="app-header">
        <div className="header-left">
          <h1>TRDR Clone</h1>
          <span className="subtitle">Real-time Trading Levels</span>
        </div>
        <div className="header-right">
          <div className={`connection-indicator ${isConnected ? 'connected' : 'disconnected'}`}>
            <div className="indicator-dot"></div>
            <span>{isConnected ? 'Live' : 'Offline'}</span>
          </div>
        </div>
      </header>

      <main className="app-main">
        <div className="chart-section">
          <Chart 
            candlesticks={candlesticks}
            topLevels={topLevels}
            onCandlestickUpdate={handleCandlestickUpdate}
          />
        </div>
        
        <Sidebar 
          topLevels={topLevels}
          isConnected={isConnected}
        />
      </main>
    </div>
  );
};

export default FullApp;