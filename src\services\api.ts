import { ChartData, Candlestick, TopLevel } from '../types';

const API_BASE = import.meta.env.VITE_API_URL || '/api';

export class ApiService {
  static async getChartData(
    interval: string = '1m',
    startTime?: number,
    endTime?: number,
    limit: number = 500
  ): Promise<ChartData> {
    const params = new URLSearchParams({
      interval,
      limit: limit.toString()
    });
    
    if (startTime) params.append('startTime', startTime.toString());
    if (endTime) params.append('endTime', endTime.toString());
    
    const response = await fetch(`${API_BASE}/chart-data?${params}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch chart data: ${response.statusText}`);
    }
    
    return response.json();
  }
  
  static async getCandlesticks(
    interval: string = '1m',
    startTime?: number,
    endTime?: number,
    limit: number = 500
  ): Promise<Candlestick[]> {
    const params = new URLSearchParams({
      interval,
      limit: limit.toString()
    });
    
    if (startTime) params.append('startTime', startTime.toString());
    if (endTime) params.append('endTime', endTime.toString());
    
    const response = await fetch(`${API_BASE}/candlesticks?${params}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch candlesticks: ${response.statusText}`);
    }
    
    return response.json();
  }
  
  static async getTopLevels(
    startTime?: number,
    endTime?: number,
    activeOnly: boolean = true
  ): Promise<TopLevel[]> {
    const params = new URLSearchParams({
      activeOnly: activeOnly.toString()
    });
    
    if (startTime) params.append('startTime', startTime.toString());
    if (endTime) params.append('endTime', endTime.toString());
    
    const response = await fetch(`${API_BASE}/top-levels?${params}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch top levels: ${response.statusText}`);
    }
    
    return response.json();
  }
  
  static async getHealth(): Promise<{ status: string; timestamp: number; binanceConnected: boolean }> {
    const response = await fetch(`${API_BASE}/health`);
    if (!response.ok) {
      throw new Error(`Health check failed: ${response.statusText}`);
    }
    
    return response.json();
  }
}