# TRDR Clone Architecture

## Data Storage Strategy

### Option 1: SQLite + JSON Files (Recommended for <PERSON>)
**Pros:**
- Local storage, no external dependencies
- Fast queries for time-series data
- JSON for complex order book data
- Easy to backup and migrate

**Cons:**
- Limited concurrent access
- Manual cleanup needed for old data

### Option 2: IndexedDB (Browser-only)
**Pros:**
- Client-side storage
- Good for small-medium datasets
- Built into browsers

**Cons:**
- Storage limits (~1GB typical)
- Browser-dependent
- No server-side processing

### Option 3: PostgreSQL + TimescaleDB
**Pros:**
- Excellent for time-series data
- Handles large datasets
- Advanced querying

**Cons:**
- Requires database setup
- Overkill for initial development

## Recommended Implementation: SQLite + Node.js Backend

### Database Schema

```sql
-- Candlestick data
CREATE TABLE candlesticks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp INTEGER NOT NULL,
    open REAL NOT NULL,
    high REAL NOT NULL,
    low REAL NOT NULL,
    close REAL NOT NULL,
    volume REAL NOT NULL,
    interval TEXT NOT NULL DEFAULT '1m',
    created_at INTEGER DEFAULT (strftime('%s', 'now'))
);

-- Order book snapshots (for calculating top levels)
CREATE TABLE orderbook_snapshots (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp INTEGER NOT NULL,
    bids TEXT NOT NULL, -- JSON array of [price, quantity]
    asks TEXT NOT NULL, -- JSON array of [price, quantity]
    created_at INTEGER DEFAULT (strftime('%s', 'now'))
);

-- Calculated top levels
CREATE TABLE top_levels (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp INTEGER NOT NULL,
    price REAL NOT NULL,
    level_type TEXT NOT NULL, -- 'support' or 'resistance'
    volume_sum REAL NOT NULL, -- Total volume at this level
    touch_count INTEGER DEFAULT 1, -- How many times price touched this level
    first_seen INTEGER NOT NULL, -- When this level was first identified
    last_updated INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT 1 -- Whether this level is still relevant
);

-- Indexes for performance
CREATE INDEX idx_candlesticks_timestamp ON candlesticks(timestamp);
CREATE INDEX idx_orderbook_timestamp ON orderbook_snapshots(timestamp);
CREATE INDEX idx_top_levels_timestamp ON top_levels(timestamp);
CREATE INDEX idx_top_levels_active ON top_levels(is_active, timestamp);
```

### Data Flow Architecture

```
Binance API → Data Collector → SQLite Database → WebSocket Server → React Frontend
     ↓              ↓               ↓                ↓              ↓
  REST/WS      Process & Store   Historical     Real-time      Chart Display
               Calculate Levels   Data Query     Updates        + Top Levels
```

### Components

1. **Data Collector Service** (Node.js)
   - Fetches historical data on startup
   - Maintains WebSocket connections to Binance
   - Processes order book data to calculate top levels
   - Stores data in SQLite

2. **API Server** (Express.js)
   - Serves historical data to frontend
   - WebSocket server for real-time updates
   - REST endpoints for chart data

3. **Frontend** (React + TypeScript)
   - Lightweight Charts for candlestick display
   - Custom overlay for top levels visualization
   - Real-time data consumption via WebSocket

### Top Levels Calculation Algorithm

```javascript
// Pseudo-code for identifying significant levels
function calculateTopLevels(orderBookHistory, priceHistory) {
  const levels = [];
  
  // 1. Find high-volume price clusters over time
  for (const snapshot of orderBookHistory) {
    const significantLevels = findVolumeConcentrations(snapshot);
    levels.push(...significantLevels);
  }
  
  // 2. Group nearby price levels (within 0.1% of each other)
  const clusteredLevels = clusterNearbyLevels(levels, 0.001); // 0.1%
  
  // 3. Track how often price interacts with these levels
  for (const level of clusteredLevels) {
    level.touchCount = countPriceTouches(level.price, priceHistory);
    level.relevanceScore = calculateRelevance(level);
  }
  
  // 4. Filter to top N most significant levels
  return clusteredLevels
    .filter(level => level.relevanceScore > threshold)
    .sort((a, b) => b.relevanceScore - a.relevanceScore)
    .slice(0, 20); // Top 20 levels
}
```

### Data Retention Strategy

- Keep 7 days of minute-level candlestick data
- Keep 1 day of order book snapshots (every 30 seconds)
- Keep 30 days of calculated top levels
- Automatic cleanup of old data

### File Structure

```
src/
├── services/
│   ├── DataCollector.ts      # Binance API integration
│   ├── LevelCalculator.ts    # Top levels algorithm
│   ├── DatabaseService.ts    # SQLite operations
│   └── WebSocketServer.ts    # Real-time data server
├── components/
│   ├── Chart.tsx            # Main chart component
│   ├── TopLevelsOverlay.tsx # Custom levels visualization
│   └── Sidebar.tsx          # Controls and indicators
└── types/
    ├── Candlestick.ts       # Data type definitions
    ├── OrderBook.ts
    └── TopLevel.ts
```

## Alternative: Client-Only Implementation

For simpler deployment, could implement entirely in browser:
- Use IndexedDB for local storage
- Fetch data directly from Binance API (CORS permitting)
- All processing happens client-side

**Trade-offs:**
- Simpler deployment
- Limited by browser storage
- No data persistence between sessions
- CORS issues with direct API calls