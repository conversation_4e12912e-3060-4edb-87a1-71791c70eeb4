import sqlite3 from 'sqlite3';
import { readFileSync } from 'fs';
import { join } from 'path';
import { Candlestick, OrderBookSnapshot, TopLevel } from '../types';

export class DatabaseService {
  private db: sqlite3.Database;
  private isInitialized = false;

  constructor(dbPath: string = './data/trdr.db') {
    this.db = new sqlite3.Database(dbPath, (err) => {
      if (err) {
        console.error('Error opening database:', err);
      } else {
        console.log('Connected to SQLite database');
        this.initializeDatabase();
      }
    });
  }

  private async initializeDatabase(): Promise<void> {
    return new Promise((resolve, reject) => {
      const schemaPath = join(__dirname, '../db/schema.sql');
      const schema = readFileSync(schemaPath, 'utf8');
      
      this.db.exec(schema, (err) => {
        if (err) {
          console.error('Error initializing database:', err);
          reject(err);
        } else {
          console.log('Database initialized successfully');
          this.isInitialized = true;
          resolve();
        }
      });
    });
  }

  async waitForInitialization(): Promise<void> {
    return new Promise((resolve) => {
      const checkInterval = setInterval(() => {
        if (this.isInitialized) {
          clearInterval(checkInterval);
          resolve();
        }
      }, 10);
    });
  }

  async insertCandlestick(candlestick: Candlestick): Promise<void> {
    await this.waitForInitialization();
    
    return new Promise((resolve, reject) => {
      const stmt = this.db.prepare(`
        INSERT OR REPLACE INTO candlesticks 
        (timestamp, open, high, low, close, volume, interval)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      
      stmt.run([
        candlestick.timestamp,
        candlestick.open,
        candlestick.high,
        candlestick.low,
        candlestick.close,
        candlestick.volume,
        candlestick.interval
      ], (err) => {
        if (err) {
          reject(err);
        } else {
          resolve();
        }
      });
      
      stmt.finalize();
    });
  }

  async insertCandlesticks(candlesticks: Candlestick[]): Promise<void> {
    await this.waitForInitialization();
    
    return new Promise((resolve, reject) => {
      this.db.serialize(() => {
        this.db.run('BEGIN TRANSACTION');
        
        const stmt = this.db.prepare(`
          INSERT OR REPLACE INTO candlesticks 
          (timestamp, open, high, low, close, volume, interval)
          VALUES (?, ?, ?, ?, ?, ?, ?)
        `);
        
        candlesticks.forEach((candlestick) => {
          stmt.run([
            candlestick.timestamp,
            candlestick.open,
            candlestick.high,
            candlestick.low,
            candlestick.close,
            candlestick.volume,
            candlestick.interval
          ]);
        });
        
        stmt.finalize();
        
        this.db.run('COMMIT', (err) => {
          if (err) {
            reject(err);
          } else {
            resolve();
          }
        });
      });
    });
  }

  async getCandlesticks(
    interval: string = '1m',
    startTime?: number,
    endTime?: number,
    limit: number = 1000
  ): Promise<Candlestick[]> {
    await this.waitForInitialization();
    
    return new Promise((resolve, reject) => {
      let query = `
        SELECT * FROM candlesticks 
        WHERE interval = ?
      `;
      const params: any[] = [interval];
      
      if (startTime) {
        query += ' AND timestamp >= ?';
        params.push(startTime);
      }
      
      if (endTime) {
        query += ' AND timestamp <= ?';
        params.push(endTime);
      }
      
      query += ' ORDER BY timestamp DESC LIMIT ?';
      params.push(limit);
      
      this.db.all(query, params, (err, rows: any[]) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows as Candlestick[]);
        }
      });
    });
  }

  async insertOrderBookSnapshot(snapshot: OrderBookSnapshot): Promise<void> {
    await this.waitForInitialization();
    
    return new Promise((resolve, reject) => {
      const stmt = this.db.prepare(`
        INSERT INTO orderbook_snapshots 
        (timestamp, bids, asks)
        VALUES (?, ?, ?)
      `);
      
      stmt.run([
        snapshot.timestamp,
        JSON.stringify(snapshot.bids),
        JSON.stringify(snapshot.asks)
      ], (err) => {
        if (err) {
          reject(err);
        } else {
          resolve();
        }
      });
      
      stmt.finalize();
    });
  }

  async getOrderBookSnapshots(
    startTime?: number,
    endTime?: number,
    limit: number = 100
  ): Promise<OrderBookSnapshot[]> {
    await this.waitForInitialization();
    
    return new Promise((resolve, reject) => {
      let query = 'SELECT * FROM orderbook_snapshots WHERE 1=1';
      const params: any[] = [];
      
      if (startTime) {
        query += ' AND timestamp >= ?';
        params.push(startTime);
      }
      
      if (endTime) {
        query += ' AND timestamp <= ?';
        params.push(endTime);
      }
      
      query += ' ORDER BY timestamp DESC LIMIT ?';
      params.push(limit);
      
      this.db.all(query, params, (err, rows: any[]) => {
        if (err) {
          reject(err);
        } else {
          const snapshots = rows.map(row => ({
            ...row,
            bids: JSON.parse(row.bids),
            asks: JSON.parse(row.asks)
          }));
          resolve(snapshots as OrderBookSnapshot[]);
        }
      });
    });
  }

  async insertTopLevel(level: TopLevel): Promise<void> {
    await this.waitForInitialization();
    
    return new Promise((resolve, reject) => {
      const stmt = this.db.prepare(`
        INSERT OR REPLACE INTO top_levels 
        (timestamp, price, level_type, volume_sum, touch_count, first_seen, last_updated, is_active)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `);
      
      stmt.run([
        level.timestamp,
        level.price,
        level.level_type,
        level.volume_sum,
        level.touch_count,
        level.first_seen,
        level.last_updated,
        level.is_active ? 1 : 0
      ], (err) => {
        if (err) {
          reject(err);
        } else {
          resolve();
        }
      });
      
      stmt.finalize();
    });
  }

  async insertTopLevels(levels: TopLevel[]): Promise<void> {
    await this.waitForInitialization();
    
    return new Promise((resolve, reject) => {
      this.db.serialize(() => {
        this.db.run('BEGIN TRANSACTION');
        
        const stmt = this.db.prepare(`
          INSERT OR REPLACE INTO top_levels 
          (timestamp, price, level_type, volume_sum, touch_count, first_seen, last_updated, is_active)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `);
        
        levels.forEach((level) => {
          stmt.run([
            level.timestamp,
            level.price,
            level.level_type,
            level.volume_sum,
            level.touch_count,
            level.first_seen,
            level.last_updated,
            level.is_active ? 1 : 0
          ]);
        });
        
        stmt.finalize();
        
        this.db.run('COMMIT', (err) => {
          if (err) {
            reject(err);
          } else {
            resolve();
          }
        });
      });
    });
  }

  async getTopLevels(
    startTime?: number,
    endTime?: number,
    activeOnly: boolean = true
  ): Promise<TopLevel[]> {
    await this.waitForInitialization();
    
    return new Promise((resolve, reject) => {
      let query = 'SELECT * FROM top_levels WHERE 1=1';
      const params: any[] = [];
      
      if (activeOnly) {
        query += ' AND is_active = 1';
      }
      
      if (startTime) {
        query += ' AND timestamp >= ?';
        params.push(startTime);
      }
      
      if (endTime) {
        query += ' AND timestamp <= ?';
        params.push(endTime);
      }
      
      query += ' ORDER BY volume_sum DESC';
      
      this.db.all(query, params, (err, rows: any[]) => {
        if (err) {
          reject(err);
        } else {
          const levels = rows.map(row => ({
            ...row,
            is_active: row.is_active === 1
          }));
          resolve(levels as TopLevel[]);
        }
      });
    });
  }

  async getLatestCandlestick(interval: string = '1m'): Promise<Candlestick | null> {
    await this.waitForInitialization();
    
    return new Promise((resolve, reject) => {
      this.db.get(
        'SELECT * FROM candlesticks WHERE interval = ? ORDER BY timestamp DESC LIMIT 1',
        [interval],
        (err, row: any) => {
          if (err) {
            reject(err);
          } else {
            resolve(row as Candlestick || null);
          }
        }
      );
    });
  }

  async close(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.db.close((err) => {
        if (err) {
          reject(err);
        } else {
          console.log('Database connection closed');
          resolve();
        }
      });
    });
  }
}