import { TopLevel, Candlestick } from '../types';

export interface LevelSnapshot {
  level: TopLevel;
  timestamp: number;
  candlestickIndex: number;
  isActive: boolean;
}

export interface LevelHistoryEntry {
  levelId: string;
  snapshots: LevelSnapshot[];
}

class LevelHistoryService {
  private history: Map<string, LevelSnapshot[]> = new Map();
  private candlestickTimestamps: number[] = [];

  /**
   * Update the level history with new levels and candlestick data
   */
  updateHistory(levels: TopLevel[], candlesticks: Candlestick[]): void {
    // Update candlestick timestamps for indexing
    this.candlestickTimestamps = candlesticks.map(c => c.timestamp);
    
    const currentTimestamp = Date.now();
    const currentCandlestickIndex = candlesticks.length - 1;

    // Process each level
    levels.forEach(level => {
      const levelId = this.getLevelId(level);
      
      if (!this.history.has(levelId)) {
        this.history.set(levelId, []);
      }

      const snapshots = this.history.get(levelId)!;
      
      // Check if we need to add a new snapshot
      const lastSnapshot = snapshots[snapshots.length - 1];
      const shouldAddSnapshot = !lastSnapshot || 
        lastSnapshot.level.price !== level.price ||
        lastSnapshot.isActive !== level.is_active ||
        (currentTimestamp - lastSnapshot.timestamp) > 30 * 60 * 1000; // 30 minutes

      if (shouldAddSnapshot) {
        snapshots.push({
          level: { ...level },
          timestamp: currentTimestamp,
          candlestickIndex: currentCandlestickIndex,
          isActive: level.is_active
        });

        // Keep only last 100 snapshots per level to prevent memory bloat
        if (snapshots.length > 100) {
          snapshots.splice(0, snapshots.length - 100);
        }
      }
    });

    // Clean up old inactive levels
    this.cleanupOldLevels();
  }

  /**
   * Get historical snapshots for visualization
   */
  getHistoryForVisualization(candlesticks: Candlestick[]): LevelHistoryEntry[] {
    const result: LevelHistoryEntry[] = [];
    
    this.history.forEach((snapshots, levelId) => {
      // Filter snapshots that are relevant to current candlestick range
      const relevantSnapshots = snapshots.filter(snapshot => {
        const candlestickIndex = this.findCandlestickIndex(snapshot.timestamp, candlesticks);
        return candlestickIndex >= 0 && candlestickIndex < candlesticks.length;
      });

      if (relevantSnapshots.length > 0) {
        result.push({
          levelId,
          snapshots: relevantSnapshots
        });
      }
    });

    return result;
  }

  /**
   * Get level segments for drawing historical lines
   */
  getLevelSegments(candlesticks: Candlestick[]): Array<{
    level: TopLevel;
    startIndex: number;
    endIndex: number;
    price: number;
  }> {
    const segments: Array<{
      level: TopLevel;
      startIndex: number;
      endIndex: number;
      price: number;
    }> = [];

    const historyEntries = this.getHistoryForVisualization(candlesticks);

    historyEntries.forEach(entry => {
      const { snapshots } = entry;
      
      // Group consecutive snapshots with same price into segments
      let currentSegment: {
        level: TopLevel;
        startIndex: number;
        endIndex: number;
        price: number;
      } | null = null;

      snapshots.forEach((snapshot, i) => {
        const candlestickIndex = this.findCandlestickIndex(snapshot.timestamp, candlesticks);
        
        if (candlestickIndex >= 0 && snapshot.isActive) {
          if (!currentSegment || currentSegment.price !== snapshot.level.price) {
            // Start new segment
            if (currentSegment) {
              segments.push(currentSegment);
            }
            
            currentSegment = {
              level: snapshot.level,
              startIndex: candlestickIndex,
              endIndex: candlestickIndex,
              price: snapshot.level.price
            };
          } else {
            // Extend current segment
            currentSegment.endIndex = candlestickIndex;
          }
        }
      });

      // Add the last segment
      if (currentSegment) {
        segments.push(currentSegment);
      }
    });

    return segments;
  }

  private getLevelId(level: TopLevel): string {
    // Create a unique ID based on level type and approximate price
    // This helps track the same conceptual level even if price shifts slightly
    const priceGroup = Math.round(level.price / 100) * 100; // Group by $100 increments
    return `${level.level_type}-${priceGroup}`;
  }

  private findCandlestickIndex(timestamp: number, candlesticks: Candlestick[]): number {
    // Find the closest candlestick index for a given timestamp
    let closestIndex = -1;
    let minDiff = Infinity;

    candlesticks.forEach((candle, index) => {
      const diff = Math.abs(candle.timestamp - timestamp);
      if (diff < minDiff) {
        minDiff = diff;
        closestIndex = index;
      }
    });

    // Only return index if within reasonable time range (2 hours)
    return minDiff < 2 * 60 * 60 * 1000 ? closestIndex : -1;
  }

  private cleanupOldLevels(): void {
    const cutoffTime = Date.now() - 24 * 60 * 60 * 1000; // 24 hours ago
    
    this.history.forEach((snapshots, levelId) => {
      // Remove snapshots older than cutoff
      const filteredSnapshots = snapshots.filter(snapshot => 
        snapshot.timestamp > cutoffTime
      );
      
      if (filteredSnapshots.length === 0) {
        this.history.delete(levelId);
      } else {
        this.history.set(levelId, filteredSnapshots);
      }
    });
  }

  /**
   * Clear all history (useful for testing or reset)
   */
  clearHistory(): void {
    this.history.clear();
    this.candlestickTimestamps = [];
  }

  /**
   * Get statistics about stored history
   */
  getStats(): {
    totalLevels: number;
    totalSnapshots: number;
    oldestSnapshot: number | null;
    newestSnapshot: number | null;
  } {
    let totalSnapshots = 0;
    let oldestSnapshot: number | null = null;
    let newestSnapshot: number | null = null;

    this.history.forEach(snapshots => {
      totalSnapshots += snapshots.length;
      
      snapshots.forEach(snapshot => {
        if (oldestSnapshot === null || snapshot.timestamp < oldestSnapshot) {
          oldestSnapshot = snapshot.timestamp;
        }
        if (newestSnapshot === null || snapshot.timestamp > newestSnapshot) {
          newestSnapshot = snapshot.timestamp;
        }
      });
    });

    return {
      totalLevels: this.history.size,
      totalSnapshots,
      oldestSnapshot,
      newestSnapshot
    };
  }
}

// Export singleton instance
export const levelHistoryService = new LevelHistoryService();
