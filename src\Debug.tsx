console.log('1. Debug.tsx file loaded');

import React from 'react';
console.log('2. React imported successfully');

import ReactDOM from 'react-dom/client';
console.log('3. ReactDOM imported successfully');

const DebugApp: React.FC = () => {
  console.log('4. DebugApp component function called');
  
  const [apiStatus, setApiStatus] = React.useState('Not tested');
  const [apiData, setApiData] = React.useState<any>(null);
  
  React.useEffect(() => {
    console.log('5. useEffect hook executed - component mounted!');
    
    // Test API connection
    const testAPI = async () => {
      try {
        setApiStatus('Testing...');
        console.log('Testing API connection...');
        
        const response = await fetch('http://localhost:8080/api/health');
        const data = await response.json();
        
        setApiStatus('✅ API Connected');
        setApiData(data);
        console.log('API test successful:', data);
        
      } catch (error) {
        setApiStatus('❌ API Failed');
        console.error('API test failed:', error);
      }
    };
    
    testAPI();
  }, []);

  console.log('6. About to render component');
  
  return (
    <div style={{
      backgroundColor: '#ff00ff', // Bright magenta
      color: 'white',
      padding: '50px',
      fontSize: '20px',
      fontFamily: 'Arial, sans-serif',
      minHeight: '100vh'
    }}>
      <h1>🔧 DEBUG MODE ACTIVE</h1>
      <div style={{ marginTop: '20px' }}>
        <p>✅ React component is rendering</p>
        <p>✅ Styles are applying</p>
        <p>✅ TypeScript is compiling</p>
        <p>🌐 API Status: {apiStatus}</p>
        <p>Time: {new Date().toLocaleString()}</p>
      </div>
      
      {apiData && (
        <div style={{
          marginTop: '20px',
          background: 'rgba(0,255,0,0.2)',
          padding: '15px',
          borderRadius: '8px'
        }}>
          <h3>API Response:</h3>
          <pre style={{ fontSize: '14px', overflow: 'auto' }}>
            {JSON.stringify(apiData, null, 2)}
          </pre>
        </div>
      )}
      
      <div style={{
        marginTop: '30px',
        background: 'rgba(0,0,0,0.3)',
        padding: '20px',
        borderRadius: '8px'
      }}>
        <h3>Browser Info:</h3>
        <p>User Agent: {navigator.userAgent}</p>
        <p>URL: {window.location.href}</p>
        <p>Screen: {window.screen.width}x{window.screen.height}</p>
      </div>

      <button 
        onClick={() => {
          console.log('Button clicked!');
          alert('Button works! React events are functioning.');
        }}
        style={{
          marginTop: '20px',
          padding: '15px 30px',
          fontSize: '16px',
          backgroundColor: '#00ff00',
          color: 'black',
          border: 'none',
          borderRadius: '5px',
          cursor: 'pointer'
        }}
      >
        Test Button
      </button>
    </div>
  );
};

console.log('7. Component defined, about to create root');

try {
  const root = ReactDOM.createRoot(document.getElementById('root')!);
  console.log('8. Root created successfully');
  
  root.render(
    <React.StrictMode>
      <DebugApp />
    </React.StrictMode>
  );
  console.log('9. Render called successfully');
} catch (error) {
  console.error('ERROR during React setup:', error);
  
  // Fallback: create element directly
  const rootDiv = document.getElementById('root');
  if (rootDiv) {
    rootDiv.innerHTML = `
      <div style="background: red; color: white; padding: 50px; font-size: 24px;">
        <h1>REACT FAILED TO LOAD</h1>
        <p>Error: ${error}</p>
        <p>Check browser console for details.</p>
      </div>
    `;
  }
}