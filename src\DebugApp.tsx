import React, { useState, useEffect } from 'react';
import { ApiService } from './services/api';

const DebugApp: React.FC = () => {
  const [status, setStatus] = useState('Loading...');
  const [apiData, setApiData] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const testApi = async () => {
      try {
        console.log('Testing API connection...');
        setStatus('Testing API...');
        
        // Test health endpoint first
        const health = await ApiService.getHealth();
        console.log('Health check result:', health);
        setStatus('Health check passed');
        
        // Test chart data
        const chartData = await ApiService.getChartData();
        console.log('Chart data result:', chartData);
        setApiData(chartData);
        setStatus('Chart data loaded successfully');
        
      } catch (err) {
        console.error('API test failed:', err);
        setError(err instanceof Error ? err.message : String(err));
        setStatus('API test failed');
      }
    };

    testApi();
  }, []);

  return (
    <div style={{ 
      padding: '20px', 
      color: 'white', 
      backgroundColor: '#0a0a0a', 
      minHeight: '100vh',
      fontFamily: 'monospace'
    }}>
      <h1>TRDR Clone - Debug Mode</h1>
      <p><strong>Status:</strong> {status}</p>
      
      {error && (
        <div style={{ 
          color: '#ff4444', 
          background: '#2a0000', 
          padding: '10px', 
          marginTop: '10px',
          borderRadius: '4px'
        }}>
          <strong>Error:</strong> {error}
        </div>
      )}
      
      {apiData && (
        <div style={{ marginTop: '20px' }}>
          <h3>API Data Received:</h3>
          <div style={{ 
            background: '#1a1a1a', 
            padding: '10px', 
            borderRadius: '4px',
            fontSize: '12px'
          }}>
            <p>Candlesticks: {apiData.candlesticks?.length || 0}</p>
            <p>Top Levels: {apiData.topLevels?.length || 0}</p>
            {apiData.candlesticks?.length > 0 && (
              <p>Latest Price: ${apiData.candlesticks[apiData.candlesticks.length - 1]?.close}</p>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default DebugApp;