import { useEffect, useRef, useState } from 'react';
import { WebSocketService } from '../services/websocket';

export const useWebSocket = (url?: string) => {
  const [isConnected, setIsConnected] = useState(false);
  const wsRef = useRef<WebSocketService | null>(null);

  useEffect(() => {
    const ws = new WebSocketService(url);
    wsRef.current = ws;

    const connect = async () => {
      try {
        await ws.connect();
        setIsConnected(true);
      } catch (error) {
        console.error('Failed to connect to WebSocket:', error);
        setIsConnected(false);
      }
    };

    connect();

    return () => {
      ws.disconnect();
      setIsConnected(false);
    };
  }, [url]);

  const subscribe = (event: string, callback: Function) => {
    if (wsRef.current) {
      wsRef.current.on(event, callback);
    }
  };

  const unsubscribe = (event: string, callback: Function) => {
    if (wsRef.current) {
      wsRef.current.off(event, callback);
    }
  };

  return {
    isConnected,
    subscribe,
    unsubscribe,
    ws: wsRef.current
  };
};