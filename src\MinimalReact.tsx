import React from 'react'
import ReactDOM from 'react-dom/client'

// Inline component without external imports
const App = () => {
  React.useEffect(() => {
    console.log('React component mounted successfully!');
  }, []);

  return React.createElement('div', {
    style: {
      backgroundColor: '#00ff00',
      color: 'black', 
      padding: '50px',
      fontSize: '24px',
      textAlign: 'center',
      minHeight: '100vh'
    }
  }, [
    React.createElement('h1', { key: 'title' }, 'MINIMAL REACT TEST'),
    React.createElement('p', { key: 'p1' }, 'React is working!'),
    React.createElement('p', { key: 'p2' }, `Time: ${new Date().toLocaleString()}`),
    React.createElement('button', {
      key: 'btn',
      onClick: () => alert('React event handling works!'),
      style: { padding: '10px 20px', fontSize: '16px' }
    }, 'Click me!')
  ]);
};

ReactDOM.createRoot(document.getElementById('root')!).render(
  React.createElement(React.StrictMode, null, React.createElement(App))
);