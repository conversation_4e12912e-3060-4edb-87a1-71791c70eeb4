import React, { useState, useEffect } from 'react';

const TestApp: React.FC = () => {
  const [status, setStatus] = useState('Starting...');
  const [apiTest, setApiTest] = useState<any>(null);

  useEffect(() => {
    const testConnection = async () => {
      try {
        setStatus('Testing API connection...');
        
        // Test if backend is reachable
        const response = await fetch('http://localhost:8080/api/health');
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        setApiTest(data);
        setStatus('Backend connected successfully!');
        
      } catch (error) {
        console.error('API Test Error:', error);
        setStatus(`Backend connection failed: ${error}`);
      }
    };

    testConnection();
  }, []);

  return (
    <div style={{
      padding: '40px',
      backgroundColor: '#0a0a0a',
      color: 'white',
      minHeight: '100vh',
      fontFamily: 'Arial, sans-serif'
    }}>
      <h1 style={{ color: '#22c55e' }}>TRDR Clone - Debug Mode</h1>
      
      <div style={{
        background: '#161616',
        padding: '20px',
        borderRadius: '8px',
        margin: '20px 0',
        border: '1px solid #333'
      }}>
        <h3>Connection Status</h3>
        <p style={{ 
          color: status.includes('failed') ? '#ff4444' : 
                status.includes('successfully') ? '#22c55e' : '#ffa500'
        }}>
          {status}
        </p>
      </div>

      {apiTest && (
        <div style={{
          background: '#161616',
          padding: '20px',
          borderRadius: '8px',
          margin: '20px 0',
          border: '1px solid #333'
        }}>
          <h3>Backend Response</h3>
          <pre style={{
            background: '#0a0a0a',
            padding: '10px',
            borderRadius: '4px',
            fontSize: '12px',
            overflow: 'auto'
          }}>
            {JSON.stringify(apiTest, null, 2)}
          </pre>
        </div>
      )}

      <div style={{
        background: '#161616',
        padding: '20px',
        borderRadius: '8px',
        margin: '20px 0',
        border: '1px solid #333'
      }}>
        <h3>Debug Info</h3>
        <ul>
          <li>Frontend URL: {window.location.href}</li>
          <li>Expected Backend: http://localhost:8080</li>
          <li>Time: {new Date().toLocaleString()}</li>
        </ul>
      </div>

      <button 
        onClick={() => window.location.reload()}
        style={{
          background: '#22c55e',
          color: 'black',
          border: 'none',
          padding: '12px 24px',
          borderRadius: '6px',
          cursor: 'pointer',
          fontSize: '14px',
          fontWeight: 'bold'
        }}
      >
        Refresh Test
      </button>
    </div>
  );
};

export default TestApp;