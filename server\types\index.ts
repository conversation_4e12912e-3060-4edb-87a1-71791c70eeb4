export interface Candlestick {
  id?: number;
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  interval: string;
  created_at?: number;
}

export interface OrderBookEntry {
  price: number;
  quantity: number;
}

export interface OrderBookSnapshot {
  id?: number;
  timestamp: number;
  bids: OrderBookEntry[];
  asks: OrderBookEntry[];
  created_at?: number;
}

export interface TopLevel {
  id?: number;
  timestamp: number;
  price: number;
  level_type: 'support' | 'resistance';
  volume_sum: number;
  touch_count: number;
  first_seen: number;
  last_updated: number;
  is_active: boolean;
}

export interface BinanceKlineResponse {
  e: string; // Event type
  E: number; // Event time
  s: string; // Symbol
  k: {
    t: number; // Kline start time
    T: number; // Kline close time
    s: string; // Symbol
    i: string; // Interval
    f: number; // First trade ID
    L: number; // Last trade ID
    o: string; // Open price
    c: string; // Close price
    h: string; // High price
    l: string; // Low price
    v: string; // Base asset volume
    n: number; // Number of trades
    x: boolean; // Is this kline closed?
    q: string; // Quote asset volume
    V: string; // Taker buy base asset volume
    Q: string; // Taker buy quote asset volume
    B: string; // Ignore
  };
}

export interface BinanceDepthResponse {
  e: string; // Event type
  E: number; // Event time
  T: number; // Transaction time
  s: string; // Symbol
  U: number; // First update ID in event
  u: number; // Final update ID in event
  pu: number; // Final update ID in last stream
  b: [string, string][]; // Bids to be updated
  a: [string, string][]; // Asks to be updated
}

export interface BinanceKlineData {
  openTime: number;
  open: string;
  high: string;
  low: string;
  close: string;
  volume: string;
  closeTime: number;
  quoteAssetVolume: string;
  numberOfTrades: number;
  takerBuyBaseAssetVolume: string;
  takerBuyQuoteAssetVolume: string;
  ignore: string;
}

export interface ChartData {
  candlesticks: Candlestick[];
  topLevels: TopLevel[];
}

export interface WebSocketMessage {
  type: 'candlestick' | 'topLevels' | 'orderbook';
  data: any;
  timestamp: number;
}