# API Documentation

## REST Endpoints

### Health Check
```
GET /api/health
```
Returns server status and connection information.

**Response:**
```json
{
  "status": "ok",
  "timestamp": 1699123456789,
  "binanceConnected": true
}
```

### Candlesticks
```
GET /api/candlesticks
```
Get historical candlestick data.

**Query Parameters:**
- `interval` (string, optional): Time interval (1m, 5m, 1h, etc.). Default: "1m"
- `startTime` (number, optional): Start time in milliseconds
- `endTime` (number, optional): End time in milliseconds  
- `limit` (number, optional): Number of candlesticks to return. Default: 1000

**Response:**
```json
[
  {
    "id": 1,
    "timestamp": 1699123456000,
    "open": 35000.50,
    "high": 35100.75,
    "low": 34950.25,
    "close": 35050.00,
    "volume": 125.75,
    "interval": "1m",
    "created_at": 1699123456789
  }
]
```

### Top Levels
```
GET /api/top-levels
```
Get calculated top support and resistance levels.

**Query Parameters:**
- `startTime` (number, optional): Start time in milliseconds
- `endTime` (number, optional): End time in milliseconds
- `activeOnly` (boolean, optional): Return only active levels. Default: true

**Response:**
```json
[
  {
    "id": 1,
    "timestamp": 1699123456000,
    "price": 35000.00,
    "level_type": "support",
    "volume_sum": 1500.25,
    "touch_count": 3,
    "first_seen": 1699120000000,
    "last_updated": 1699123456000,
    "is_active": true
  }
]
```

### Chart Data
```
GET /api/chart-data
```
Get combined candlestick and top levels data.

**Query Parameters:**
Same as candlesticks endpoint.

**Response:**
```json
{
  "candlesticks": [...],
  "topLevels": [...]
}
```

## WebSocket Events

Connect to `ws://localhost:8080` to receive real-time updates.

### Message Format
All WebSocket messages follow this format:
```json
{
  "type": "candlestick" | "topLevels" | "orderbook",
  "data": {...},
  "timestamp": 1699123456789
}
```

### Candlestick Updates
```json
{
  "type": "candlestick",
  "data": {
    "timestamp": 1699123456000,
    "open": 35000.50,
    "high": 35100.75,
    "low": 34950.25,
    "close": 35050.00,
    "volume": 125.75,
    "interval": "1m"
  },
  "timestamp": 1699123456789
}
```

### Top Levels Updates
```json
{
  "type": "topLevels",
  "data": [
    {
      "timestamp": 1699123456000,
      "price": 35000.00,
      "level_type": "support",
      "volume_sum": 1500.25,
      "touch_count": 3,
      "first_seen": 1699120000000,
      "last_updated": 1699123456000,
      "is_active": true
    }
  ],
  "timestamp": 1699123456789
}
```

## Error Responses

All API endpoints return appropriate HTTP status codes:

- `200` - Success
- `400` - Bad Request (invalid parameters)
- `500` - Internal Server Error

Error response format:
```json
{
  "error": "Error message description"
}
```