/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background-color: #0a0a0a;
  color: #ffffff;
  overflow: hidden;
}

.app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #0a0a0a;
}

/* Header */
.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1.5rem;
  background-color: #161616;
  border-bottom: 1px solid #2a2a2a;
  flex-shrink: 0;
}

.header-left h1 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 0.125rem;
}

.subtitle {
  font-size: 0.875rem;
  color: #888888;
}

.header-right {
  display: flex;
  align-items: center;
}

.connection-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.connection-indicator.connected {
  background-color: rgba(34, 197, 94, 0.1);
  color: #22c55e;
}

.connection-indicator.disconnected {
  background-color: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.indicator-dot {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  background-color: currentColor;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Main content */
.app-main {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.chart-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #0a0a0a;
}

/* Chart */
.chart-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 1rem;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.chart-header h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #ffffff;
}

.chart-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.price {
  font-size: 1.125rem;
  font-weight: 600;
  color: #22c55e;
}

.levels-count {
  font-size: 0.875rem;
  color: #888888;
}

.chart {
  border-radius: 0.5rem;
  overflow: hidden;
  border: 1px solid #2a2a2a;
}

/* Sidebar */
.sidebar {
  width: 320px;
  background-color: #161616;
  border-left: 1px solid #2a2a2a;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
}

.sidebar-header {
  padding: 1rem;
  border-bottom: 1px solid #2a2a2a;
}

.sidebar-header h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.connection-status.connected {
  color: #22c55e;
}

.connection-status.disconnected {
  color: #ef4444;
}

.status-dot {
  width: 0.375rem;
  height: 0.375rem;
  border-radius: 50%;
  background-color: currentColor;
}

/* Levels sections */
.levels-section {
  flex: 1;
  overflow: auto;
}

.section-title {
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  background-color: #1a1a1a;
  border-bottom: 1px solid #2a2a2a;
}

.section-title.resistance {
  color: #ef4444;
}

.section-title.support {
  color: #22c55e;
}

.levels-list {
  max-height: 300px;
  overflow-y: auto;
}

.level-item {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #2a2a2a;
  transition: background-color 0.2s;
}

.level-item:hover {
  background-color: #1a1a1a;
}

.level-price {
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.level-item.resistance .level-price {
  color: #ef4444;
}

.level-item.support .level-price {
  color: #22c55e;
}

.level-details {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
  font-size: 0.75rem;
  color: #888888;
}

.touches {
  font-weight: 500;
}

/* Sidebar footer */
.sidebar-footer {
  padding: 1rem;
  border-top: 1px solid #2a2a2a;
  background-color: #1a1a1a;
}

.stats {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
}

.stat .label {
  color: #888888;
}

.stat .value {
  color: #ffffff;
  font-weight: 600;
}

/* Loading state */
.app.loading {
  justify-content: center;
  align-items: center;
}

.loading-spinner {
  text-align: center;
}

.spinner {
  width: 2rem;
  height: 2rem;
  border: 2px solid #2a2a2a;
  border-top: 2px solid #22c55e;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-spinner p {
  color: #888888;
  font-size: 0.875rem;
}

/* Error state */
.app.error {
  justify-content: center;
  align-items: center;
}

.error-message {
  text-align: center;
  padding: 2rem;
  background-color: #161616;
  border: 1px solid #2a2a2a;
  border-radius: 0.5rem;
  max-width: 400px;
}

.error-message h2 {
  color: #ef4444;
  margin-bottom: 0.5rem;
}

.error-message p {
  color: #888888;
  margin-bottom: 1rem;
}

.error-message button {
  background-color: #22c55e;
  color: #000000;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.error-message button:hover {
  background-color: #16a34a;
}

/* Scrollbar styling */
.levels-list::-webkit-scrollbar {
  width: 0.25rem;
}

.levels-list::-webkit-scrollbar-track {
  background: #1a1a1a;
}

.levels-list::-webkit-scrollbar-thumb {
  background: #2a2a2a;
  border-radius: 0.125rem;
}

.levels-list::-webkit-scrollbar-thumb:hover {
  background: #3a3a3a;
}

/* Responsive design */
@media (max-width: 768px) {
  .app-main {
    flex-direction: column;
  }
  
  .sidebar {
    width: 100%;
    height: 200px;
    border-left: none;
    border-top: 1px solid #2a2a2a;
  }
  
  .levels-section {
    max-height: none;
  }
  
  .levels-list {
    max-height: 150px;
  }
}