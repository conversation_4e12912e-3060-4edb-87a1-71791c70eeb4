import React from 'react';
import { TopLevel } from '../types';

interface SidebarProps {
  topLevels: TopLevel[];
  isConnected: boolean;
}

export const Sidebar: React.FC<SidebarProps> = ({ topLevels, isConnected }) => {
  const supportLevels = topLevels.filter(level => 
    level.level_type === 'support' && level.is_active
  ).sort((a, b) => b.price - a.price);

  const resistanceLevels = topLevels.filter(level => 
    level.level_type === 'resistance' && level.is_active
  ).sort((a, b) => a.price - b.price);

  const formatPrice = (price: number) => `$${price.toFixed(2)}`;
  const formatVolume = (volume: number) => {
    if (volume >= 1000000) return `${(volume / 1000000).toFixed(1)}M`;
    if (volume >= 1000) return `${(volume / 1000).toFixed(1)}K`;
    return volume.toFixed(0);
  };

  const getTimeAgo = (timestamp: number) => {
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days}d ago`;
    if (hours > 0) return `${hours}h ago`;
    if (minutes > 0) return `${minutes}m ago`;
    return 'Just now';
  };

  return (
    <div className="sidebar">
      <div className="sidebar-header">
        <h3>Top Levels</h3>
        <div className={`connection-status ${isConnected ? 'connected' : 'disconnected'}`}>
          <div className="status-dot"></div>
          {isConnected ? 'Connected' : 'Disconnected'}
        </div>
      </div>

      <div className="levels-section">
        <h4 className="section-title resistance">Resistance Levels</h4>
        <div className="levels-list">
          {resistanceLevels.slice(0, 10).map((level, index) => (
            <div key={level.id || index} className="level-item resistance">
              <div className="level-price">{formatPrice(level.price)}</div>
              <div className="level-details">
                <span className="touches">{level.touch_count} touches</span>
                <span className="volume">Vol: {formatVolume(level.volume_sum)}</span>
                <span className="time">{getTimeAgo(level.last_updated)}</span>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="levels-section">
        <h4 className="section-title support">Support Levels</h4>
        <div className="levels-list">
          {supportLevels.slice(0, 10).map((level, index) => (
            <div key={level.id || index} className="level-item support">
              <div className="level-price">{formatPrice(level.price)}</div>
              <div className="level-details">
                <span className="touches">{level.touch_count} touches</span>
                <span className="volume">Vol: {formatVolume(level.volume_sum)}</span>
                <span className="time">{getTimeAgo(level.last_updated)}</span>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="sidebar-footer">
        <div className="stats">
          <div className="stat">
            <span className="label">Active Levels:</span>
            <span className="value">{topLevels.filter(l => l.is_active).length}</span>
          </div>
          <div className="stat">
            <span className="label">Support:</span>
            <span className="value">{supportLevels.length}</span>
          </div>
          <div className="stat">
            <span className="label">Resistance:</span>
            <span className="value">{resistanceLevels.length}</span>
          </div>
        </div>
      </div>
    </div>
  );
};