// Shared types with backend
export interface Candlestick {
  id?: number;
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  interval: string;
  created_at?: number;
}

export interface OrderBookEntry {
  price: number;
  quantity: number;
}

export interface TopLevel {
  id?: number;
  timestamp: number;
  price: number;
  level_type: 'support' | 'resistance';
  volume_sum: number;
  touch_count: number;
  first_seen: number;
  last_updated: number;
  is_active: boolean;
}

export interface ChartData {
  candlesticks: Candlestick[];
  topLevels: TopLevel[];
}

export interface WebSocketMessage {
  type: 'candlestick' | 'topLevels' | 'orderbook';
  data: any;
  timestamp: number;
}

// Lightweight Charts specific types
export interface LightweightCandlestick {
  time: number;
  open: number;
  high: number;
  low: number;
  close: number;
}

export interface PriceLine {
  price: number;
  color: string;
  lineWidth: number;
  lineStyle: number;
  axisLabelVisible: boolean;
  title: string;
}