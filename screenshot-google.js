const puppeteer = require('puppeteer');
const path = require('path');

async function takeScreenshot() {
  console.log('Launching browser...');
  
  // Launch browser
  const browser = await puppeteer.launch({
    headless: 'new',
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    console.log('Creating new page...');
    const page = await browser.newPage();
    
    // Set viewport size
    await page.setViewport({
      width: 1280,
      height: 720
    });
    
    console.log('Navigating to Google.com...');
    await page.goto('https://www.google.com', {
      waitUntil: 'networkidle2',
      timeout: 30000
    });
    
    // Wait a bit for the page to fully load
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const screenshotPath = path.join(__dirname, 'google-screenshot.png');
    console.log(`Taking screenshot and saving to: ${screenshotPath}`);
    
    await page.screenshot({
      path: screenshotPath,
      fullPage: true
    });
    
    console.log('Screenshot saved successfully!');
    return screenshotPath;
    
  } catch (error) {
    console.error('Error taking screenshot:', error);
    throw error;
  } finally {
    await browser.close();
    console.log('Browser closed.');
  }
}

// Run the function
takeScreenshot()
  .then((path) => {
    console.log(`Screenshot completed: ${path}`);
  })
  .catch((error) => {
    console.error('Failed to take screenshot:', error);
    process.exit(1);
  });