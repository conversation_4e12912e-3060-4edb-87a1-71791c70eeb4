import React, { useMemo, useEffect } from 'react';
import { Candlestick, TopLevel } from '../types';
import { levelHistoryService } from '../services/levelHistory';

interface EnhancedChartProps {
  candlesticks: Candlestick[];
  topLevels: TopLevel[];
}

const EnhancedChart: React.FC<EnhancedChartProps> = ({ candlesticks, topLevels }) => {
  const latestPrice = candlesticks.length > 0 ? candlesticks[candlesticks.length - 1].close : 0;
  const priceChange = candlesticks.length > 1 ?
    candlesticks[candlesticks.length - 1].close - candlesticks[candlesticks.length - 2].close : 0;

  // Update level history whenever levels or candlesticks change
  useEffect(() => {
    if (candlesticks.length > 0 && topLevels.length > 0) {
      levelHistoryService.updateHistory(topLevels, candlesticks);
    }
  }, [topLevels, candlesticks]);

  // Calculate chart dimensions and scaling
  const chartData = useMemo(() => {
    if (candlesticks.length === 0) return null;

    const maxPrice = Math.max(...candlesticks.map(c => Math.max(c.high, c.open, c.close, c.low)));
    const minPrice = Math.min(...candlesticks.map(c => Math.min(c.high, c.open, c.close, c.low)));
    const priceRange = maxPrice - minPrice;
    const padding = priceRange * 0.05;
    const chartMax = maxPrice + padding;
    const chartMin = minPrice - padding;
    const chartRange = chartMax - chartMin;

    const getY = (price: number) => {
      return ((chartMax - price) / chartRange) * 100;
    };

    const getX = (index: number) => {
      return (index / candlesticks.length) * 100;
    };

    return {
      maxPrice,
      minPrice,
      chartMax,
      chartMin,
      chartRange,
      getY,
      getX
    };
  }, [candlesticks]);

  // Get historical level segments for visualization
  const levelSegments = useMemo(() => {
    if (!chartData || candlesticks.length === 0) return [];

    return levelHistoryService.getLevelSegments(candlesticks);
  }, [chartData, candlesticks]);

  if (!chartData) {
    return (
      <div style={{ 
        background: '#1a1a1a', 
        borderRadius: '6px', 
        padding: '16px',
        height: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: '#666'
      }}>
        Loading chart data...
      </div>
    );
  }

  const candleWidth = Math.max(1, Math.min(8, 100 / candlesticks.length * 0.6));

  return (
    <div style={{ 
      background: '#1a1a1a', 
      borderRadius: '6px', 
      padding: '16px',
      height: '100%',
      display: 'flex',
      flexDirection: 'column'
    }}>
      {/* Chart Header */}
      <div style={{ marginBottom: '16px', flexShrink: 0 }}>
        <h2 style={{ 
          margin: '0 0 8px 0', 
          color: 'white', 
          fontSize: '16px',
          fontWeight: '600'
        }}>BTCUSDT Perpetual (30m)</h2>
        <div style={{ display: 'flex', gap: '12px', alignItems: 'center' }}>
          <span style={{
            fontSize: '24px',
            fontWeight: '700',
            color: priceChange >= 0 ? '#22c55e' : '#ef4444'
          }}>
            ${latestPrice.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
          </span>
          <span style={{
            color: priceChange >= 0 ? '#22c55e' : '#ef4444',
            fontSize: '12px',
            background: priceChange >= 0 ? 'rgba(34, 197, 94, 0.1)' : 'rgba(239, 68, 68, 0.1)',
            padding: '2px 6px',
            borderRadius: '3px'
          }}>
            {priceChange >= 0 ? '+' : ''}{priceChange.toFixed(2)}
          </span>
          <span style={{ color: '#666', fontSize: '12px' }}>
            {topLevels.filter(l => l.is_active).length} Active Levels
          </span>
          <span style={{ color: '#666', fontSize: '12px' }}>
            Vol: {candlesticks.length > 0 ? candlesticks[candlesticks.length - 1].volume.toFixed(0) : '0'}
          </span>
        </div>
      </div>

      {/* Chart Area */}
      <div style={{
        flex: 1,
        position: 'relative',
        background: '#0f0f0f',
        borderRadius: '4px',
        overflow: 'hidden',
        minHeight: '400px'
      }}>
        <svg width="100%" height="100%" style={{ position: 'absolute' }} viewBox="0 0 100 100" preserveAspectRatio="none">
          {/* Price Grid Lines */}
          {Array.from({ length: 6 }, (_, i) => {
            const price = chartData.chartMin + (chartData.chartRange * i / 5);
            const y = Math.max(5, Math.min(90, chartData.getY(price)));
            return (
              <g key={`grid-${i}`}>
                <line
                  x1="8%"
                  y1={`${y}%`}
                  x2="100%"
                  y2={`${y}%`}
                  stroke="#2a2a2a"
                  strokeWidth="1"
                  opacity="0.3"
                />
                <text
                  x="2%"
                  y={`${y + 1}%`}
                  fill="#666"
                  fontSize="10"
                  textAnchor="start"
                >
                  ${price.toFixed(0)}
                </text>
              </g>
            );
          })}

          {/* Historical Level Lines */}
          {levelSegments.map((segment, segmentIndex) => {
            const { level, startIndex, endIndex, price } = segment;
            const y = Math.max(5, Math.min(90, chartData.getY(price)));
            const startX = chartData.getX(startIndex);
            const endX = chartData.getX(endIndex);
            const isSupport = level.level_type === 'support';

            return (
              <g key={`level-segment-${segmentIndex}`}>
                <line
                  x1={`${startX}%`}
                  y1={`${y}%`}
                  x2={`${endX}%`}
                  y2={`${y}%`}
                  stroke={isSupport ? '#22c55e' : '#ef4444'}
                  strokeWidth="2"
                  strokeDasharray="3,3"
                  opacity="0.8"
                />
                <rect
                  x={`${Math.max(0, endX - 12)}%`}
                  y={`${Math.max(5, y - 1.5)}%`}
                  width="11%"
                  height="3%"
                  fill={isSupport ? '#22c55e' : '#ef4444'}
                  opacity="0.2"
                  rx="2"
                />
                <text
                  x={`${Math.max(5.5, endX - 6.5)}%`}
                  y={`${Math.max(6.5, y + 0.5)}%`}
                  fill={isSupport ? '#22c55e' : '#ef4444'}
                  fontSize="10"
                  textAnchor="middle"
                  fontWeight="500"
                >
                  ${price.toFixed(2)}
                </text>
              </g>
            );
          })}

          {/* Time axis labels */}
          {Array.from({ length: 6 }, (_, i) => {
            const candleIndex = Math.floor((candlesticks.length - 1) * i / 5);
            if (candleIndex >= 0 && candleIndex < candlesticks.length) {
              const candle = candlesticks[candleIndex];
              const x = chartData.getX(candleIndex);
              const time = new Date(candle.timestamp).toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
              });

              return (
                <g key={`time-${i}`}>
                  <line
                    x1={`${x}%`}
                    y1="95%"
                    x2={`${x}%`}
                    y2="97%"
                    stroke="#666"
                    strokeWidth="1"
                  />
                  <text
                    x={`${x}%`}
                    y="99%"
                    fill="#666"
                    fontSize="8"
                    textAnchor="middle"
                  >
                    {time}
                  </text>
                </g>
              );
            }
            return null;
          })}

          {/* Candlesticks */}
          {candlesticks.map((candle, i) => {
            const x = chartData.getX(i);
            const isGreen = candle.close > candle.open;
            const bodyTop = Math.min(candle.open, candle.close);
            const bodyBottom = Math.max(candle.open, candle.close);
            const bodyHeight = Math.abs(chartData.getY(bodyBottom) - chartData.getY(bodyTop));

            return (
              <g key={`candle-${i}`}>
                {/* Wick */}
                <line
                  x1={`${x + candleWidth/2}%`}
                  y1={`${Math.max(5, chartData.getY(candle.high))}%`}
                  x2={`${x + candleWidth/2}%`}
                  y2={`${Math.min(90, chartData.getY(candle.low))}%`}
                  stroke={isGreen ? '#22c55e' : '#ef4444'}
                  strokeWidth="1"
                />
                {/* Body */}
                <rect
                  x={`${x}%`}
                  y={`${Math.max(5, Math.min(90, chartData.getY(bodyTop)))}%`}
                  width={`${candleWidth}%`}
                  height={`${Math.max(0.5, Math.min(85, bodyHeight))}%`}
                  fill={isGreen ? '#22c55e' : '#ef4444'}
                  stroke={isGreen ? '#22c55e' : '#ef4444'}
                  strokeWidth="1"
                  opacity="0.9"
                />
              </g>
            );
          })}
        </svg>
      </div>
    </div>
  );
};

export default EnhancedChart;
