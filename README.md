# TRDR Clone

## Overview
A clone of the TRDR.io trading platform featuring:
- Real-time candlestick charts
- Top Levels indicator showing key orderbook levels over time
- Support/resistance level visualization
- TradingView-style interface
- Time-based price action analysis

This clone focuses on the visual interface and charting capabilities without trading logic implementation.

## Project Structure
```
├── src/           # Source code
├── docs/          # Documentation
├── tests/         # Test files
├── config/        # Configuration files
├── assets/        # Static assets (images, etc.)
├── scripts/       # Build and utility scripts
└── README.md      # This file
```

## Requirements

- Node.js 18+ 
- NPM or Yarn
- Internet connection for Binance API access

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd trdr
```

2. Install dependencies:
```bash
npm install
```

3. Copy environment variables:
```bash
cp .env.example .env
```

4. Create data directory:
```bash
mkdir -p data
```

## Usage

### Development Mode
Start both the backend server and frontend development server:
```bash
npm run dev
```

This will start:
- Backend server on `http://localhost:8080`
- Frontend development server on `http://localhost:5173`
- Real-time data collection from Binance API

### Production Build
```bash
npm run build
npm start
```

### Individual Services
```bash
# Start only the backend server
npm run server:dev

# Start only the frontend development server
npm run client:dev
```

## Features

### Real-time Data
- Live BTCUSDT perpetual futures data from Binance
- WebSocket connections for real-time updates
- Historical data fetching and storage

### Top Levels Algorithm
- Identifies significant support and resistance levels
- Volume-based level detection
- Price touch tracking and level validation
- Automatic level expiration and cleanup

### Chart Interface
- Lightweight Charts integration
- Real-time candlestick updates
- Interactive price level overlays
- Support/resistance level visualization

### Data Storage
- SQLite database for historical data
- Automatic data cleanup (7 days candlesticks, 1 day order book, 30 days levels)
- Efficient time-series data storage

## Contributing
[Contributing guidelines]

## License
[License information]