import React, { useEffect, useRef, useState } from 'react';
import { create<PERSON>hart, IChartApi, ISeriesApi, LineStyle, Time } from 'lightweight-charts';
import { Candlestick, TopLevel } from '../types';

interface ChartProps {
  candlesticks: Candlestick[];
  topLevels: TopLevel[];
  onCandlestickUpdate?: (candlestick: Candlestick) => void;
}

export const Chart: React.FC<ChartProps> = ({ 
  candlesticks, 
  topLevels, 
  onCandlestickUpdate 
}) => {
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<IChartApi | null>(null);
  const candlestickSeriesRef = useRef<ISeriesApi<'Candlestick'> | null>(null);
  const [priceLines, setPriceLines] = useState<Map<number, any>>(new Map());

  useEffect(() => {
    if (!chartContainerRef.current) return;

    // Create chart
    const chart = createChart(chartContainerRef.current, {
      width: chartContainerRef.current.clientWidth,
      height: 600,
      layout: {
        background: { color: '#1e1e1e' },
        textColor: '#d1d4dc',
      },
      grid: {
        vertLines: { color: '#2b2b43' },
        horzLines: { color: '#2b2b43' },
      },
      crosshair: {
        mode: 1,
      },
      rightPriceScale: {
        borderColor: '#485c7b',
      },
      timeScale: {
        borderColor: '#485c7b',
        timeVisible: true,
        secondsVisible: false,
      },
    });

    // Create candlestick series
    const candlestickSeries = chart.addCandlestickSeries({
      upColor: '#26a69a',
      downColor: '#ef5350',
      borderVisible: false,
      wickUpColor: '#26a69a',
      wickDownColor: '#ef5350',
    });

    chartRef.current = chart;
    candlestickSeriesRef.current = candlestickSeries;

    // Handle resize
    const handleResize = () => {
      if (chartContainerRef.current) {
        chart.applyOptions({
          width: chartContainerRef.current.clientWidth,
        });
      }
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      chart.remove();
    };
  }, []);

  // Update candlesticks
  useEffect(() => {
    if (!candlestickSeriesRef.current || !candlesticks.length) return;

    const lightweightData = candlesticks.map(candle => ({
      time: Math.floor(candle.timestamp / 1000) as Time,
      open: candle.open,
      high: candle.high,
      low: candle.low,
      close: candle.close,
    }));

    candlestickSeriesRef.current.setData(lightweightData);
  }, [candlesticks]);

  // Update top levels
  useEffect(() => {
    if (!chartRef.current) return;

    // Clear existing price lines
    priceLines.forEach((line) => {
      candlestickSeriesRef.current?.removePriceLine(line);
    });
    
    const newPriceLines = new Map();

    // Add new price lines
    topLevels.forEach((level) => {
      if (!level.is_active) return;

      const color = level.level_type === 'support' ? '#26a69a' : '#ef5350';
      const lineStyle = level.touch_count >= 3 ? LineStyle.Solid : LineStyle.Dashed;
      
      const priceLine = candlestickSeriesRef.current?.createPriceLine({
        price: level.price,
        color: color,
        lineWidth: 2,
        lineStyle: lineStyle,
        axisLabelVisible: true,
        title: `${level.level_type.toUpperCase()} (${level.touch_count} touches)`,
      });

      if (priceLine) {
        newPriceLines.set(level.price, priceLine);
      }
    });

    setPriceLines(newPriceLines);
  }, [topLevels]);

  // Handle real-time candlestick updates
  useEffect(() => {
    if (onCandlestickUpdate && candlestickSeriesRef.current) {
      // This would be called from the parent component when new data arrives
      // The actual update logic is handled in the parent
    }
  }, [onCandlestickUpdate]);

  return (
    <div className="chart-container">
      <div className="chart-header">
        <h2>BTCUSDT Perpetual</h2>
        <div className="chart-info">
          <span className="price">
            {candlesticks.length > 0 ? `$${candlesticks[candlesticks.length - 1]?.close.toFixed(2)}` : '--'}
          </span>
          <span className="levels-count">
            {topLevels.filter(l => l.is_active).length} Active Levels
          </span>
        </div>
      </div>
      <div 
        ref={chartContainerRef} 
        className="chart"
        style={{ width: '100%', height: '600px' }}
      />
    </div>
  );
};