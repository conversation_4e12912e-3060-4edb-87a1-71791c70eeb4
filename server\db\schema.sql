-- TRDR Clone Database Schema

-- Candlestick data
CREATE TABLE IF NOT EXISTS candlesticks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp INTEGER NOT NULL,
    open REAL NOT NULL,
    high REAL NOT NULL,
    low REAL NOT NULL,
    close REAL NOT NULL,
    volume REAL NOT NULL,
    interval TEXT NOT NULL DEFAULT '1m',
    created_at INTEGER DEFAULT (strftime('%s', 'now'))
);

-- Order book snapshots (for calculating top levels)
CREATE TABLE IF NOT EXISTS orderbook_snapshots (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp INTEGER NOT NULL,
    bids TEXT NOT NULL, -- JSON array of [price, quantity]
    asks TEXT NOT NULL, -- JSON array of [price, quantity]
    created_at INTEGER DEFAULT (strftime('%s', 'now'))
);

-- Calculated top levels
CREATE TABLE IF NOT EXISTS top_levels (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp INTEGER NOT NULL,
    price REAL NOT NULL,
    level_type TEXT NOT NULL, -- 'support' or 'resistance'
    volume_sum REAL NOT NULL, -- Total volume at this level
    touch_count INTEGER DEFAULT 1, -- How many times price touched this level
    first_seen INTEGER NOT NULL, -- When this level was first identified
    last_updated INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT 1 -- Whether this level is still relevant
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_candlesticks_timestamp ON candlesticks(timestamp);
CREATE INDEX IF NOT EXISTS idx_candlesticks_interval_timestamp ON candlesticks(interval, timestamp);
CREATE INDEX IF NOT EXISTS idx_orderbook_timestamp ON orderbook_snapshots(timestamp);
CREATE INDEX IF NOT EXISTS idx_top_levels_timestamp ON top_levels(timestamp);
CREATE INDEX IF NOT EXISTS idx_top_levels_active ON top_levels(is_active, timestamp);
CREATE INDEX IF NOT EXISTS idx_top_levels_price ON top_levels(price, is_active);

-- Data cleanup triggers (automatically remove old data)
CREATE TRIGGER IF NOT EXISTS cleanup_old_candlesticks 
AFTER INSERT ON candlesticks
BEGIN
    DELETE FROM candlesticks 
    WHERE timestamp < (strftime('%s', 'now') - 172800); -- 2 days in seconds
END;

CREATE TRIGGER IF NOT EXISTS cleanup_old_orderbook_snapshots 
AFTER INSERT ON orderbook_snapshots
BEGIN
    DELETE FROM orderbook_snapshots 
    WHERE timestamp < (strftime('%s', 'now') - 43200); -- 12 hours in seconds
END;

CREATE TRIGGER IF NOT EXISTS cleanup_old_top_levels 
AFTER INSERT ON top_levels
BEGIN
    DELETE FROM top_levels 
    WHERE timestamp < (strftime('%s', 'now') - 259200); -- 3 days in seconds
END;