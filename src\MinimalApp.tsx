import React, { useState, useEffect } from 'react';
import { ApiService } from './services/api';
import { Candlestick, TopLevel } from './types';

const MinimalApp: React.FC = () => {
  const [candlesticks, setCandlesticks] = useState<Candlestick[]>([]);
  const [topLevels, setTopLevels] = useState<TopLevel[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadData = async () => {
      try {
        console.log('Loading data...');
        const data = await ApiService.getChartData();
        console.log('Data loaded:', data);
        setCandlesticks(data.candlesticks);
        setTopLevels(data.topLevels);
        setLoading(false);
      } catch (err) {
        console.error('Error:', err);
        setError(String(err));
        setLoading(false);
      }
    };
    loadData();
  }, []);

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh', 
        backgroundColor: '#0a0a0a',
        color: 'white'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{
            width: '50px',
            height: '50px',
            border: '3px solid #333',
            borderTop: '3px solid #22c55e',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 20px'
          }}></div>
          <p>Loading TRDR data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh', 
        backgroundColor: '#0a0a0a',
        color: 'white'
      }}>
        <div style={{ 
          textAlign: 'center',
          background: '#2a0000',
          padding: '20px',
          borderRadius: '8px',
          border: '1px solid #ff4444'
        }}>
          <h2 style={{ color: '#ff4444' }}>Connection Error</h2>
          <p>{error}</p>
          <button 
            onClick={() => window.location.reload()}
            style={{
              background: '#22c55e',
              color: 'black',
              border: 'none',
              padding: '10px 20px',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  const latestPrice = candlesticks.length > 0 ? candlesticks[candlesticks.length - 1].close : 0;

  return (
    <div style={{ 
      backgroundColor: '#0a0a0a', 
      color: 'white', 
      minHeight: '100vh',
      fontFamily: 'Arial, sans-serif'
    }}>
      <header style={{ 
        background: '#161616', 
        padding: '20px', 
        borderBottom: '1px solid #333'
      }}>
        <h1>TRDR Clone</h1>
        <p style={{ color: '#888', margin: '5px 0 0 0' }}>Real-time Trading Levels</p>
      </header>

      <main style={{ padding: '20px' }}>
        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: '1fr 300px', 
          gap: '20px',
          height: 'calc(100vh - 120px)'
        }}>
          
          {/* Chart Area */}
          <div style={{ 
            background: '#161616', 
            borderRadius: '8px', 
            padding: '20px',
            border: '1px solid #333'
          }}>
            <div style={{ marginBottom: '20px' }}>
              <h2 style={{ margin: '0 0 10px 0' }}>BTCUSDT Perpetual</h2>
              <div style={{ display: 'flex', gap: '20px', alignItems: 'center' }}>
                <span style={{ 
                  fontSize: '24px', 
                  fontWeight: 'bold',
                  color: '#22c55e'
                }}>
                  ${latestPrice.toFixed(2)}
                </span>
                <span style={{ color: '#888' }}>
                  {topLevels.filter(l => l.is_active).length} Active Levels
                </span>
              </div>
            </div>
            
            {/* Placeholder for chart */}
            <div style={{ 
              background: '#0a0a0a',
              height: '400px',
              border: '1px solid #333',
              borderRadius: '4px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: '#666'
            }}>
              <div style={{ textAlign: 'center' }}>
                <p>📈 Chart will be rendered here</p>
                <p>Data loaded: {candlesticks.length} candlesticks</p>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div style={{ 
            background: '#161616', 
            borderRadius: '8px', 
            padding: '20px',
            border: '1px solid #333'
          }}>
            <h3 style={{ margin: '0 0 20px 0' }}>Top Levels</h3>
            
            <div style={{ marginBottom: '20px' }}>
              <h4 style={{ 
                color: '#ff4444', 
                fontSize: '14px', 
                textTransform: 'uppercase',
                margin: '0 0 10px 0'
              }}>
                Resistance ({topLevels.filter(l => l.level_type === 'resistance' && l.is_active).length})
              </h4>
              {topLevels
                .filter(l => l.level_type === 'resistance' && l.is_active)
                .slice(0, 5)
                .map((level, i) => (
                  <div key={i} style={{ 
                    padding: '8px 0', 
                    borderBottom: '1px solid #333' 
                  }}>
                    <div style={{ 
                      fontWeight: 'bold', 
                      color: '#ff4444' 
                    }}>
                      ${level.price.toFixed(2)}
                    </div>
                    <div style={{ 
                      fontSize: '12px', 
                      color: '#888' 
                    }}>
                      {level.touch_count} touches
                    </div>
                  </div>
                ))}
            </div>

            <div>
              <h4 style={{ 
                color: '#22c55e', 
                fontSize: '14px', 
                textTransform: 'uppercase',
                margin: '0 0 10px 0'
              }}>
                Support ({topLevels.filter(l => l.level_type === 'support' && l.is_active).length})
              </h4>
              {topLevels
                .filter(l => l.level_type === 'support' && l.is_active)
                .slice(0, 5)
                .map((level, i) => (
                  <div key={i} style={{ 
                    padding: '8px 0', 
                    borderBottom: '1px solid #333' 
                  }}>
                    <div style={{ 
                      fontWeight: 'bold', 
                      color: '#22c55e' 
                    }}>
                      ${level.price.toFixed(2)}
                    </div>
                    <div style={{ 
                      fontSize: '12px', 
                      color: '#888' 
                    }}>
                      {level.touch_count} touches
                    </div>
                  </div>
                ))}
            </div>
          </div>
        </div>
      </main>

      <style>
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}
      </style>
    </div>
  );
};

export default MinimalApp;