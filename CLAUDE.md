# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

TRDR Clone is a real-time trading platform that displays BTCUSDT candlestick charts with support/resistance level detection. It streams live data from Binance API and calculates "Top Levels" using order book analysis.

## Development Commands

```bash
# Development (runs both client and server concurrently)
npm run dev

# Individual services
npm run client:dev    # Frontend only (port 3000)
npm run server:dev    # Backend only (port 8080)

# Build and type checking
npm run build         # Production build
npm run type-check    # TypeScript validation
npm run lint          # ESLint validation
npm run preview       # Preview production build
```

## Architecture

**Full-stack TypeScript application:**
- **Frontend**: React 18 + Vite + LightweightCharts + WebSocket client
- **Backend**: Node.js + Express + WebSocket server + SQLite
- **Data Flow**: Binance API → Data Collector → Database → WebSocket → React Charts

**Key Services:**
- `server/services/BinanceDataCollector.ts`: Fetches real-time market data
- `server/services/TopLevelsCalculator.ts`: Calculates support/resistance levels
- `server/services/DatabaseService.ts`: SQLite operations with auto-cleanup
- `src/services/websocket.ts`: Real-time client updates
- `src/components/Chart.tsx`: LightweightCharts integration

**Database Schema** (`server/db/schema.sql`):
- `candlesticks`: OHLCV data (2-day retention)
- `orderbook_snapshots`: Order book data (12-hour retention) 
- `top_levels`: Support/resistance levels (3-day retention)

## Development Setup

1. Install dependencies: `npm install`
2. Create data directory: `mkdir -p data`
3. Run development servers: `npm run dev`

**Ports:**
- Frontend: http://localhost:3000
- Backend API: http://localhost:8080
- WebSocket: ws://localhost:8080

## Key Development Patterns

**Real-time Data Handling:**
- Server maintains persistent Binance WebSocket connections with auto-reconnect
- Client receives updates via WebSocket and updates charts without full re-renders
- Database automatically cleans up old data to maintain performance

**Component Architecture:**
- `App.tsx`: Main application state and WebSocket connection
- `Chart.tsx`: Handles LightweightCharts instance and real-time updates
- `Sidebar.tsx`: Displays calculated top levels and controls

**API Endpoints:**
- `/api/health`: Server and Binance connection status
- `/api/candlesticks`: Historical data queries
- `/api/top-levels`: Current support/resistance levels
- `/api/chart-data`: Combined candlesticks and levels

**Configuration:**
- Vite proxy routes API and WebSocket requests to backend
- TypeScript configs: root for client, `server/tsconfig.json` for backend
- Environment variables for API URLs (defaults to localhost)