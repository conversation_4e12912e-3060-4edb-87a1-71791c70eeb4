import express from 'express';
import { createServer } from 'http';
import { WebSocketServer, WebSocket } from 'ws';
import cors from 'cors';
import { existsSync, mkdirSync } from 'fs';
import { join } from 'path';

import { DatabaseService } from './services/DatabaseService';
import { BinanceDataCollector } from './services/BinanceDataCollector';
import { TopLevelsCalculator } from './services/TopLevelsCalculator';
import { ChartData, WebSocketMessage } from './types';

class TRDRServer {
  private app: express.Application;
  private server: any;
  private wss: WebSocketServer;
  private db: DatabaseService;
  private dataCollector: BinanceDataCollector;
  private levelsCalculator: TopLevelsCalculator;
  private port: number;

  constructor(port: number = 8080) {
    this.port = port;
    this.app = express();
    this.server = createServer(this.app);
    this.wss = new WebSocketServer({ server: this.server });
    
    // Initialize services
    this.initializeDataDirectory();
    this.db = new DatabaseService();
    this.dataCollector = new BinanceDataCollector();
    this.levelsCalculator = new TopLevelsCalculator();
    
    this.setupMiddleware();
    this.setupRoutes();
    this.setupWebSocket();
    this.setupEventHandlers();
  }

  private initializeDataDirectory(): void {
    const dataDir = join(process.cwd(), 'data');
    if (!existsSync(dataDir)) {
      mkdirSync(dataDir, { recursive: true });
      console.log('Created data directory');
    }
  }

  private setupMiddleware(): void {
    this.app.use(cors());
    this.app.use(express.json());
    this.app.use(express.static('dist'));
  }

  private setupRoutes(): void {
    // Health check
    this.app.get('/api/health', (_req, res) => {
      res.json({
        status: 'ok',
        timestamp: Date.now(),
        binanceConnected: this.dataCollector.isConnected()
      });
    });

    // Get historical candlestick data
    this.app.get('/api/candlesticks', async (req, res) => {
      try {
        const {
          interval = '1m',
          startTime,
          endTime,
          limit = 500
        } = req.query;

        const candlesticks = await this.db.getCandlesticks(
          interval as string,
          startTime ? parseInt(startTime as string) : undefined,
          endTime ? parseInt(endTime as string) : undefined,
          parseInt(limit as string)
        );

        res.json(candlesticks.reverse()); // Return in chronological order
      } catch (error) {
        console.error('Error fetching candlesticks:', error);
        res.status(500).json({ error: 'Internal server error' });
      }
    });

    // Get current top levels
    this.app.get('/api/top-levels', async (req, res) => {
      try {
        const {
          startTime,
          endTime,
          activeOnly = 'true'
        } = req.query;

        const topLevels = await this.db.getTopLevels(
          startTime ? parseInt(startTime as string) : undefined,
          endTime ? parseInt(endTime as string) : undefined,
          activeOnly === 'true'
        );

        res.json(topLevels);
      } catch (error) {
        console.error('Error fetching top levels:', error);
        res.status(500).json({ error: 'Internal server error' });
      }
    });

    // Get chart data (candlesticks + top levels)
    this.app.get('/api/chart-data', async (req, res) => {
      try {
        const {
          interval = '1m',
          startTime,
          endTime,
          limit = 500
        } = req.query;

        const [candlesticks, topLevels] = await Promise.all([
          this.db.getCandlesticks(
            interval as string,
            startTime ? parseInt(startTime as string) : undefined,
            endTime ? parseInt(endTime as string) : undefined,
            parseInt(limit as string)
          ),
          this.db.getTopLevels(
            startTime ? parseInt(startTime as string) : undefined,
            endTime ? parseInt(endTime as string) : undefined,
            true
          )
        ]);

        const chartData: ChartData = {
          candlesticks: candlesticks.reverse(),
          topLevels: topLevels
        };

        res.json(chartData);
      } catch (error) {
        console.error('Error fetching chart data:', error);
        res.status(500).json({ error: 'Internal server error' });
      }
    });
  }

  private setupWebSocket(): void {
    this.wss.on('connection', (ws) => {
      console.log('Client connected to WebSocket');

      // Send initial data
      this.sendInitialData(ws);

      ws.on('close', () => {
        console.log('Client disconnected from WebSocket');
      });

      ws.on('error', (error) => {
        console.error('WebSocket error:', error);
      });
    });
  }

  private async sendInitialData(ws: any): Promise<void> {
    try {
      // Send latest candlesticks
      const recentCandlesticks = await this.db.getCandlesticks('1m', undefined, undefined, 200);
      const topLevels = await this.db.getTopLevels(undefined, undefined, true);

      const initialData: ChartData = {
        candlesticks: recentCandlesticks.reverse(),
        topLevels: topLevels
      };

      if (ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify({
          type: 'candlestick',
          data: initialData,
          timestamp: Date.now()
        }));
      }
    } catch (error) {
      console.error('Error sending initial data:', error);
    }
  }

  private setupEventHandlers(): void {
    // Handle historical candlesticks from data collector
    this.dataCollector.on('historicalCandlesticks', async (candlesticks) => {
      try {
        await this.db.insertCandlesticks(candlesticks);
        console.log(`Stored ${candlesticks.length} historical candlesticks`);
      } catch (error) {
        console.error('Error storing historical candlesticks:', error);
      }
    });

    // Handle real-time candlestick updates
    this.dataCollector.on('candlestick', async (candlestick, isClosed) => {
      try {
        // Store completed candlesticks
        if (isClosed) {
          await this.db.insertCandlestick(candlestick);
        }

        // Process for top levels calculation
        this.levelsCalculator.processCandlestick(candlestick, isClosed);

        // Broadcast to WebSocket clients
        this.sendToWebSocket({
          type: 'candlestick',
          data: candlestick,
          timestamp: Date.now()
        });
      } catch (error) {
        console.error('Error processing candlestick:', error);
      }
    });

    // Handle order book updates
    this.dataCollector.on('orderBookUpdate', async (snapshot) => {
      try {
        // Store order book snapshots periodically (every 30 seconds)
        const now = Date.now();
        const lastSnapshot = await this.db.getOrderBookSnapshots(now - 30000, now, 1);
        
        if (lastSnapshot.length === 0) {
          await this.db.insertOrderBookSnapshot(snapshot);
        }

        // Process for top levels calculation
        this.levelsCalculator.processOrderBookSnapshot(snapshot);
      } catch (error) {
        console.error('Error processing order book update:', error);
      }
    });

    // Handle top levels updates
    this.levelsCalculator.on('topLevelsUpdate', async (topLevels) => {
      try {
        // Store updated top levels
        await this.db.insertTopLevels(topLevels);

        // Broadcast to WebSocket clients
        this.sendToWebSocket({
          type: 'topLevels',
          data: topLevels,
          timestamp: Date.now()
        });
      } catch (error) {
        console.error('Error processing top levels update:', error);
      }
    });

    // Clean up old data periodically (every hour)
    setInterval(() => {
      const oneHourAgo = Date.now() - (60 * 60 * 1000);
      this.levelsCalculator.clearOldData(oneHourAgo);
    }, 60 * 60 * 1000);
  }

  private sendToWebSocket(message: WebSocketMessage): void {
    const messageString = JSON.stringify(message);
    
    this.wss.clients.forEach((client) => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(messageString);
      }
    });
  }

  async start(): Promise<void> {
    try {
      // Wait for database to initialize
      await this.db.waitForInitialization();
      
      // Start data collection
      await this.dataCollector.start();
      
      // Start HTTP server
      this.server.listen(this.port, () => {
        console.log(`TRDR Server running on port ${this.port}`);
        console.log(`WebSocket server ready for connections`);
        console.log(`Access the application at http://localhost:${this.port}`);
      });
    } catch (error) {
      console.error('Error starting server:', error);
      process.exit(1);
    }
  }

  async stop(): Promise<void> {
    console.log('Shutting down TRDR Server...');
    
    // Stop data collection
    this.dataCollector.stop();
    
    // Close WebSocket server
    this.wss.close();
    
    // Close database
    await this.db.close();
    
    // Close HTTP server
    this.server.close();
    
    console.log('TRDR Server stopped');
  }
}

// Handle graceful shutdown
const server = new TRDRServer();

process.on('SIGINT', async () => {
  await server.stop();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await server.stop();
  process.exit(0);
});

// Start the server
server.start().catch((error) => {
  console.error('Failed to start server:', error);
  process.exit(1);
});