{"name": "trdr-clone", "version": "1.0.0", "description": "A clone of the TRDR.io trading platform", "main": "index.js", "scripts": {"dev": "concurrently \"npm run server:dev\" \"npm run client:dev\"", "client:dev": "vite", "server:dev": "nodemon --exec \"ts-node --project server/tsconfig.json server/index.ts\"", "server:build": "tsc server/index.ts --outDir dist/server", "build": "npm run server:build && vite build", "preview": "vite preview", "lint": "eslint src server --ext .js,.jsx,.ts,.tsx", "type-check": "tsc --noEmit"}, "keywords": ["trading", "charts", "orderbook", "levels"], "author": "", "license": "MIT", "dependencies": {"axios": "^1.4.0", "cors": "^2.8.5", "express": "^4.18.2", "lightweight-charts": "^4.1.0", "puppeteer": "^24.10.0", "react": "^18.2.0", "react-dom": "^18.2.0", "sqlite3": "^5.1.6", "ws": "^8.13.0"}, "devDependencies": {"@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/sqlite3": "^3.1.8", "@types/ws": "^8.5.5", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.0", "concurrently": "^8.2.0", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.0", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "typescript": "^5.0.2", "vite": "^4.4.0"}}