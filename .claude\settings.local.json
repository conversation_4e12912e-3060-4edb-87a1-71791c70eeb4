{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "WebFetch(domain:developers.binance.com)", "Bash(npm install)", "Bash(npm run type-check:*)", "Bash(npm run server:dev:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(wget:*)", "<PERSON><PERSON>(pkill:*)", "Bash(npm run dev:*)", "<PERSON><PERSON>(true)", "Bash(npm run client:dev:*)", "Bash(kill:*)", "Bash(rm:*)", "<PERSON><PERSON>(puppeteer --help)", "Bash(npm install:*)", "Bash(node:*)", "Bash(ls:*)", "<PERSON><PERSON>(env)", "WebFetch(domain:docs.anthropic.com)", "mcp__puppeteer__puppeteer_navigate", "mcp__puppeteer__puppeteer_screenshot", "Bash(find:*)", "mcp__puppeteer__puppeteer_evaluate"], "deny": []}}