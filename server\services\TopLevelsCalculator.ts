import { EventEmitter } from 'events';
import { Candlestick, OrderBookSnapshot, TopLevel, OrderBookEntry } from '../types';

interface VolumeCluster {
  price: number;
  volume: number;
  count: number;
  firstSeen: number;
  lastSeen: number;
}

interface PriceTouch {
  timestamp: number;
  price: number;
  touchType: 'support' | 'resistance';
}

export class TopLevelsCalculator extends EventEmitter {
  private volumeClusters: Map<number, VolumeCluster> = new Map();
  private priceTouches: PriceTouch[] = [];
  private currentTopLevels: TopLevel[] = [];
  private lastUpdateTime = 0;
  private currentMarketPrice = 0;
  private readonly UPDATE_THROTTLE = 5000; // Update every 5 seconds
  
  private readonly CLUSTER_TOLERANCE = 0.005; // 0.5% price tolerance for clustering
  private readonly MIN_VOLUME_THRESHOLD = 10; // Minimum volume to consider significant
  private readonly MIN_TOUCH_COUNT = 1; // Minimum touches to consider a level significant
  private readonly LEVEL_EXPIRY_TIME = 4 * 60 * 60 * 1000; // 4 hours in milliseconds
  private readonly MAX_LEVELS = 10; // Maximum number of levels to track

  constructor() {
    super();
  }

  processOrderBookSnapshot(snapshot: OrderBookSnapshot): void {
    try {
      // Process bids (potential support levels)
      if (snapshot.bids && Array.isArray(snapshot.bids)) {
        this.processOrderBookSide(snapshot.bids, 'support', snapshot.timestamp);
      }
      
      // Process asks (potential resistance levels)
      if (snapshot.asks && Array.isArray(snapshot.asks)) {
        this.processOrderBookSide(snapshot.asks, 'resistance', snapshot.timestamp);
      }
      
      // Update existing levels (throttled)
      this.maybeUpdateTopLevels(snapshot.timestamp);
    } catch (error) {
      console.error('Error processing order book snapshot:', error);
    }
  }

  processCandlestick(candlestick: Candlestick, isClosed: boolean): void {
    // Update current market price
    this.currentMarketPrice = candlestick.close;
    
    if (!isClosed) return; // Only process completed candles
    
    // Detect price touches at significant levels
    this.detectPriceTouches(candlestick);
    
    // Update level relevance based on price action
    this.updateLevelRelevance(candlestick);
  }

  private processOrderBookSide(
    orders: OrderBookEntry[], 
    levelType: 'support' | 'resistance',
    timestamp: number
  ): void {
    if (!orders || orders.length === 0) return;
    
    // Sort orders by volume (highest first) and only take significant volumes
    const sortedOrders = orders
      .filter(order => order.quantity >= this.MIN_VOLUME_THRESHOLD)
      .sort((a, b) => b.quantity - a.quantity)
      .slice(0, 5); // Consider only top 5 orders to reduce noise

    for (const order of sortedOrders) {
      // Only process orders with substantial volume
      if (order.quantity >= this.MIN_VOLUME_THRESHOLD * 2) {
        this.addVolumeCluster(order.price, order.quantity, timestamp);
      }
    }
  }

  private addVolumeCluster(price: number, volume: number, timestamp: number): void {
    // Find existing cluster within tolerance
    const existingCluster = this.findNearbyCluster(price);
    
    if (existingCluster) {
      // Find the key for this cluster
      let clusterId: number | null = null;
      for (const [key, cluster] of this.volumeClusters.entries()) {
        if (cluster === existingCluster) {
          clusterId = key;
          break;
        }
      }
      
      if (clusterId !== null) {
        // Weighted average for price
        const totalVolume = existingCluster.volume + volume;
        existingCluster.price = (existingCluster.price * existingCluster.volume + price * volume) / totalVolume;
        existingCluster.volume = totalVolume;
        existingCluster.count += 1;
        existingCluster.lastSeen = timestamp;
        
        this.volumeClusters.set(clusterId, existingCluster);
      }
    } else {
      // Create new cluster
      this.volumeClusters.set(price, {
        price: price,
        volume: volume,
        count: 1,
        firstSeen: timestamp,
        lastSeen: timestamp
      });
    }
  }

  private findNearbyCluster(price: number): VolumeCluster | null {
    for (const cluster of this.volumeClusters.values()) {
      const priceDiff = Math.abs(cluster.price - price) / price;
      if (priceDiff <= this.CLUSTER_TOLERANCE) {
        return cluster;
      }
    }
    return null;
  }

  private detectPriceTouches(candlestick: Candlestick): void {
    // Check if price touched any significant levels
    for (const level of this.currentTopLevels) {
      const levelTolerance = level.price * 0.002; // 0.2% tolerance
      
      // Check if candle high/low touched the level
      const touchedHigh = Math.abs(candlestick.high - level.price) <= levelTolerance;
      const touchedLow = Math.abs(candlestick.low - level.price) <= levelTolerance;
      
      if (touchedHigh || touchedLow) {
        const touchType = candlestick.close > level.price ? 'support' : 'resistance';
        
        this.priceTouches.push({
          timestamp: candlestick.timestamp,
          price: level.price,
          touchType: touchType
        });
        
        // Update level touch count
        level.touch_count += 1;
        level.last_updated = candlestick.timestamp;
      }
    }
    
    // Clean old touches (keep only last hour)
    const oneHourAgo = candlestick.timestamp - (60 * 60 * 1000);
    this.priceTouches = this.priceTouches.filter(touch => touch.timestamp > oneHourAgo);
  }

  private updateLevelRelevance(candlestick: Candlestick): void {
    // Reduce relevance of levels that haven't been touched recently
    const currentTime = candlestick.timestamp;
    
    for (const level of this.currentTopLevels) {
      const timeSinceLastTouch = currentTime - level.last_updated;
      
      // Deactivate levels that are too old
      if (timeSinceLastTouch > this.LEVEL_EXPIRY_TIME) {
        level.is_active = false;
      }
    }
  }

  private maybeUpdateTopLevels(timestamp: number): void {
    // Throttle updates to prevent spam
    if (timestamp - this.lastUpdateTime < this.UPDATE_THROTTLE) {
      return;
    }
    this.lastUpdateTime = timestamp;
    this.updateTopLevels(timestamp);
  }

  private updateTopLevels(timestamp: number): void {
    const newLevels: TopLevel[] = [];
    const processedPrices = new Set<string>(); // Track processed price ranges to avoid duplicates
    
    // Convert volume clusters to top levels
    for (const cluster of this.volumeClusters.values()) {
      // Skip clusters that are too old
      const age = timestamp - cluster.lastSeen;
      if (age > this.LEVEL_EXPIRY_TIME) {
        continue;
      }
      
      // Skip if we already processed a nearby price level
      const priceKey = Math.round(cluster.price / (cluster.price * this.CLUSTER_TOLERANCE)).toString();
      if (processedPrices.has(priceKey)) {
        continue;
      }
      processedPrices.add(priceKey);
      
      // Calculate relevance score
      const relevanceScore = this.calculateRelevanceScore(cluster, timestamp);
      
      // Apply 5% price filtering - only include levels within 5% of current market price
      const priceVariation = this.currentMarketPrice > 0 ? 
        Math.abs(cluster.price - this.currentMarketPrice) / this.currentMarketPrice : 1;
      
      if (relevanceScore > 0.3 && 
          cluster.volume > this.MIN_VOLUME_THRESHOLD && 
          priceVariation <= 0.05) { // 5% price filter
        // Determine level type based on recent price action
        const levelType = this.determineLevelType(cluster.price);
        
        // Create new level
        const touchCount = Math.max(1, this.countTouchesNearPrice(cluster.price));
        const newLevel: TopLevel = {
          timestamp: timestamp,
          price: Math.round(cluster.price * 100) / 100, // Round to 2 decimal places
          level_type: levelType,
          volume_sum: cluster.volume,
          touch_count: touchCount,
          first_seen: cluster.firstSeen,
          last_updated: timestamp,
          is_active: true
        };
        newLevels.push(newLevel);
      }
    }
    
    // Sort by volume and relevance, keep only top levels
    newLevels.sort((a, b) => {
      const scoreA = this.calculateLevelScore(a);
      const scoreB = this.calculateLevelScore(b);
      return scoreB - scoreA;
    });
    
    // Filter out levels that are too close to each other
    const filteredLevels: TopLevel[] = [];
    for (const level of newLevels) {
      const isTooClose = filteredLevels.some(existing => 
        Math.abs(existing.price - level.price) / level.price < this.CLUSTER_TOLERANCE
      );
      
      if (!isTooClose && filteredLevels.length < this.MAX_LEVELS) {
        filteredLevels.push(level);
      }
    }
    
    this.currentTopLevels = filteredLevels;
    
    // Debug logging
    console.log(`Generated ${this.currentTopLevels.length} top levels from ${this.volumeClusters.size} clusters`);
    
    // Only emit if we have meaningful levels
    if (this.currentTopLevels.length > 0) {
      this.emit('topLevelsUpdate', this.currentTopLevels);
    } else {
      // If no levels generated, create some basic levels around current price for demonstration
      console.log('No meaningful levels found, generating demo levels');
      this.generateDemoLevels(timestamp);
    }
  }

  private calculateRelevanceScore(cluster: VolumeCluster, currentTime: number): number {
    const age = currentTime - cluster.lastSeen;
    const ageScore = Math.max(0, 1 - age / this.LEVEL_EXPIRY_TIME);
    const volumeScore = Math.min(1, cluster.volume / 1000); // Normalize volume
    const frequencyScore = Math.min(1, cluster.count / 10); // Normalize frequency
    
    return (ageScore * 0.4 + volumeScore * 0.4 + frequencyScore * 0.2);
  }

  private determineLevelType(price: number): 'support' | 'resistance' {
    // Simple heuristic: determine based on recent price touches
    const recentTouches = this.priceTouches
      .filter(touch => Math.abs(touch.price - price) / price <= this.CLUSTER_TOLERANCE)
      .slice(-5); // Last 5 touches
    
    if (recentTouches.length === 0) {
      return 'support'; // Default
    }
    
    const supportTouches = recentTouches.filter(touch => touch.touchType === 'support').length;
    const resistanceTouches = recentTouches.filter(touch => touch.touchType === 'resistance').length;
    
    return supportTouches >= resistanceTouches ? 'support' : 'resistance';
  }

  private countTouchesNearPrice(price: number): number {
    return this.priceTouches.filter(touch => 
      Math.abs(touch.price - price) / price <= this.CLUSTER_TOLERANCE
    ).length;
  }

  private calculateLevelScore(level: TopLevel): number {
    const volumeScore = Math.min(1, level.volume_sum / 1000);
    const touchScore = Math.min(1, level.touch_count / this.MIN_TOUCH_COUNT);
    const ageScore = Math.max(0, 1 - (Date.now() - level.last_updated) / this.LEVEL_EXPIRY_TIME);
    
    return volumeScore * 0.4 + touchScore * 0.4 + ageScore * 0.2;
  }

  getCurrentTopLevels(): TopLevel[] {
    return this.currentTopLevels.filter(level => level.is_active);
  }

  generateDemoLevels(timestamp: number): void {
    // For demo purposes, create some levels around current market price
    const basePrice = this.currentMarketPrice > 0 ? this.currentMarketPrice : 104000;
    const demoLevels: TopLevel[] = [
      {
        timestamp,
        price: basePrice + 2000,
        level_type: 'resistance',
        volume_sum: 150,
        touch_count: 2,
        first_seen: timestamp - 3600000,
        last_updated: timestamp,
        is_active: true
      },
      {
        timestamp,
        price: basePrice + 1000,
        level_type: 'resistance', 
        volume_sum: 120,
        touch_count: 1,
        first_seen: timestamp - 1800000,
        last_updated: timestamp,
        is_active: true
      },
      {
        timestamp,
        price: basePrice - 1000,
        level_type: 'support',
        volume_sum: 180,
        touch_count: 3,
        first_seen: timestamp - 7200000,
        last_updated: timestamp,
        is_active: true
      },
      {
        timestamp,
        price: basePrice - 2000,
        level_type: 'support',
        volume_sum: 200,
        touch_count: 2,
        first_seen: timestamp - 5400000,
        last_updated: timestamp,
        is_active: true
      }
    ];

    this.currentTopLevels = demoLevels;
    this.emit('topLevelsUpdate', this.currentTopLevels);
    console.log('Generated demo levels for testing');
  }

  clearOldData(cutoffTime: number): void {
    // Clean old volume clusters
    for (const [key, cluster] of this.volumeClusters.entries()) {
      if (cluster.lastSeen < cutoffTime) {
        this.volumeClusters.delete(key);
      }
    }
    
    // Clean old price touches
    this.priceTouches = this.priceTouches.filter(touch => touch.timestamp > cutoffTime);
    
    // Deactivate old levels
    for (const level of this.currentTopLevels) {
      if (level.last_updated < cutoffTime) {
        level.is_active = false;
      }
    }
  }
}