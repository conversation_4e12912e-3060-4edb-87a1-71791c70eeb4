import WebSocket from 'ws';
import axios from 'axios';
import { EventEmitter } from 'events';
import { 
  Candlestick, 
  OrderBookSnapshot, 
  BinanceKlineResponse, 
  BinanceDepthResponse,
  BinanceKlineData 
} from '../types';

export class BinanceDataCollector extends EventEmitter {
  private klineWs: WebSocket | null = null;
  private depthWs: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 5000;
  private isCollecting = false;

  private readonly BINANCE_WS_BASE = 'wss://fstream.binance.com/ws/';
  private readonly BINANCE_API_BASE = 'https://fapi.binance.com';
  private readonly SYMBOL = 'BTCUSDT';
  private readonly INTERVAL = '30m'; // Changed to 30-minute intervals

  constructor() {
    super();
  }

  async start(): Promise<void> {
    console.log('Starting Binance data collection...');
    this.isCollecting = true;

    try {
      // Fetch initial historical data
      await this.fetchHistoricalData();
      
      // Start real-time data streams
      this.connectKlineStream();
      this.connectDepthStream();
      
      console.log('Binance data collection started successfully');
    } catch (error) {
      console.error('Error starting data collection:', error);
      throw error;
    }
  }

  stop(): void {
    console.log('Stopping Binance data collection...');
    this.isCollecting = false;
    
    if (this.klineWs) {
      this.klineWs.close();
      this.klineWs = null;
    }
    
    if (this.depthWs) {
      this.depthWs.close();
      this.depthWs = null;
    }
    
    console.log('Binance data collection stopped');
  }

  private async fetchHistoricalData(): Promise<void> {
    console.log('Fetching recent historical candlestick data...');
    
    try {
      // Fetch only yesterday and today's data
      const now = Date.now();
      const yesterday = now - (24 * 60 * 60 * 1000); // 24 hours ago
      const startTime = yesterday;
      const endTime = now;
      
      console.log(`Fetching data from ${new Date(startTime)} to ${new Date(endTime)}`);
      
      const response = await axios.get(`${this.BINANCE_API_BASE}/fapi/v1/klines`, {
        params: {
          symbol: this.SYMBOL,
          interval: this.INTERVAL,
          startTime: startTime,
          endTime: endTime,
          limit: 100 // 100 30-minute candles = ~50 hours of data
        }
      });

      const candlesticks: Candlestick[] = response.data.map((kline: any[]) => ({
        timestamp: kline[0],
        open: parseFloat(kline[1]),
        high: parseFloat(kline[2]),
        low: parseFloat(kline[3]),
        close: parseFloat(kline[4]),
        volume: parseFloat(kline[5]),
        interval: this.INTERVAL
      }));

      console.log(`Fetched ${candlesticks.length} historical candlesticks`);
      this.emit('historicalCandlesticks', candlesticks);
      
      console.log('Recent historical data fetch completed');
    } catch (error) {
      console.error('Error fetching historical data:', error);
      throw error;
    }
  }

  private connectKlineStream(): void {
    const url = `${this.BINANCE_WS_BASE}${this.SYMBOL.toLowerCase()}@kline_${this.INTERVAL}`;
    console.log('Connecting to Binance kline stream:', url);
    
    this.klineWs = new WebSocket(url);
    
    this.klineWs.on('open', () => {
      console.log('Kline WebSocket connected');
      this.reconnectAttempts = 0;
    });
    
    this.klineWs.on('message', (data: WebSocket.Data) => {
      try {
        const message: BinanceKlineResponse = JSON.parse(data.toString());
        
        if (message.e === 'kline') {
          const candlestick: Candlestick = {
            timestamp: message.k.t,
            open: parseFloat(message.k.o),
            high: parseFloat(message.k.h),
            low: parseFloat(message.k.l),
            close: parseFloat(message.k.c),
            volume: parseFloat(message.k.v),
            interval: message.k.i
          };
          
          this.emit('candlestick', candlestick, message.k.x); // x indicates if kline is closed
        }
      } catch (error) {
        console.error('Error parsing kline message:', error);
      }
    });
    
    this.klineWs.on('error', (error) => {
      console.error('Kline WebSocket error:', error);
    });
    
    this.klineWs.on('close', () => {
      console.log('Kline WebSocket disconnected');
      if (this.isCollecting) {
        this.handleReconnect('kline');
      }
    });
  }

  private connectDepthStream(): void {
    const url = `${this.BINANCE_WS_BASE}${this.SYMBOL.toLowerCase()}@depth@100ms`;
    console.log('Connecting to Binance depth stream:', url);
    
    this.depthWs = new WebSocket(url);
    
    this.depthWs.on('open', () => {
      console.log('Depth WebSocket connected');
      this.reconnectAttempts = 0;
    });
    
    this.depthWs.on('message', (data: WebSocket.Data) => {
      try {
        const message: BinanceDepthResponse = JSON.parse(data.toString());
        
        if (message.e === 'depthUpdate') {
          const snapshot: OrderBookSnapshot = {
            timestamp: message.E,
            bids: message.b.map(([price, quantity]) => ({
              price: parseFloat(price),
              quantity: parseFloat(quantity)
            })),
            asks: message.a.map(([price, quantity]) => ({
              price: parseFloat(price),
              quantity: parseFloat(quantity)
            }))
          };
          
          this.emit('orderBookUpdate', snapshot);
        }
      } catch (error) {
        console.error('Error parsing depth message:', error);
      }
    });
    
    this.depthWs.on('error', (error) => {
      console.error('Depth WebSocket error:', error);
    });
    
    this.depthWs.on('close', () => {
      console.log('Depth WebSocket disconnected');
      if (this.isCollecting) {
        this.handleReconnect('depth');
      }
    });
  }

  private handleReconnect(streamType: 'kline' | 'depth'): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error(`Max reconnection attempts reached for ${streamType} stream`);
      return;
    }
    
    this.reconnectAttempts++;
    console.log(`Attempting to reconnect ${streamType} stream (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
    
    setTimeout(() => {
      if (streamType === 'kline') {
        this.connectKlineStream();
      } else {
        this.connectDepthStream();
      }
    }, this.reconnectDelay);
  }

  async fetchCurrentOrderBook(): Promise<OrderBookSnapshot> {
    try {
      const response = await axios.get(`${this.BINANCE_API_BASE}/fapi/v1/depth`, {
        params: {
          symbol: this.SYMBOL,
          limit: 100
        }
      });

      return {
        timestamp: Date.now(),
        bids: response.data.bids.map(([price, quantity]: [string, string]) => ({
          price: parseFloat(price),
          quantity: parseFloat(quantity)
        })),
        asks: response.data.asks.map(([price, quantity]: [string, string]) => ({
          price: parseFloat(price),
          quantity: parseFloat(quantity)
        }))
      };
    } catch (error) {
      console.error('Error fetching order book:', error);
      throw error;
    }
  }

  isConnected(): boolean {
    return (
      this.klineWs?.readyState === WebSocket.OPEN &&
      this.depthWs?.readyState === WebSocket.OPEN
    );
  }
}